import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule, DatePipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { AvailabilityType, InstructorDetails, InstructorInformationParams, Instructors } from '../../models';
import { CommonUtils } from 'src/app/shared/utils';
import { MatIconModule } from '@angular/material/icon';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { AuthService } from 'src/app/auth/services';
import { takeUntil } from 'rxjs';
import { CBGetResponse, CBResponse } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { DashIfEmptyPipe } from 'src/app/shared/pipe';
import moment from 'moment';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import {
  AdvancedFilters,
  ClassTypes,
  ScheduleDetailsView,
  ScheduleFilters
} from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { Debounce } from 'src/app/shared/decorators';
import { MatSidenavModule } from '@angular/material/sidenav';
import { InstructorUnderSupervisorComponent } from '../../../supervisors/pages/instructor-under-supervisor/instructor-under-supervisor.component';
import { EChartsOption } from 'echarts';
import { LeaveRequestService } from 'src/app/pages/requests/pages/leave-request/services';
import { LeaveBalance } from 'src/app/pages/requests/pages/leave-request/models';
import { ChatService } from 'src/app/pages/messages/services';
import { ChatHistoryRes, ChatMessageType, FileType } from 'src/app/pages/messages/models';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MessagesComponent } from "../../../../../messages/messages.component";
import { IdEmailModel } from '../../../students/models';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { Account } from 'src/app/auth/models/user.model';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule, MatIconModule, MatIconModule, MatSidenavModule, MatTooltipModule],
  PIPES: [DashIfEmptyPipe],
  COMPONENTS: [InstructorUnderSupervisorComponent, MessagesComponent]
};
@Component({
  selector: 'app-view-instructor',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './view-instructor.component.html',
  styleUrl: './view-instructor.component.scss'
})
export class ViewInstructorComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedInstructorViewDetails!: InstructorDetails | null;
  @Input() isSupervisor!: boolean;
  @Input() isFromScheduler!: boolean;
  @Input() currentUser$!: Account | null;

  showScheduleLoader = false;
  isBioCollapsed = true;
  isLocationCollapsed = true;
  isSideNavOpen = false;
  isMessageSideNavOpen = false;
  isEditLeaveBalance = false;
  currentPage = this.paginationConfig.pageNumber;
  pageSize = this.paginationConfig.twoItemsPerPage;
  chatTypes = ChatMessageType;
  fileTypes = FileType;
  locations!: Array<SchoolLocations>;
  chartOptions!: EChartsOption;
  usedLeaves!: number;
  availableLeaves!: number;
  totalLeaves!: number;
  leaveBalance!: LeaveBalance[];
  chatHistory!: Array<ChatHistoryRes>;
  noIntroductoryInstrumentAvailable?: boolean;
  classTypes = ClassTypes;
  filters: InstructorInformationParams = {
    startDate: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd) ?? '',
    endDate: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd) ?? ''
  };
  appliedAdvanceFilter = new AdvancedFilters();
  events!: Array<ScheduleDetailsView>;
  selectedIdEmail!: IdEmailModel | null;

  @Output() closeViewSideNav = new EventEmitter<void>();
  @Output() openEditSideNav = new EventEmitter<void>();

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly authService: AuthService,
    private readonly datePipe: DatePipe,
    private readonly schedulerService: SchedulerService,
    private readonly leaveRequestService: LeaveRequestService,
    private readonly chatService: ChatService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getAllLocations();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.showPageLoader = true;
    if (changes['selectedInstructorViewDetails']?.currentValue) {
      this.selectedInstructorViewDetails = changes['selectedInstructorViewDetails'].currentValue;
      this.loadMessages();
      this.resetFiltersToToday();
      this.getDependentSchedule();
      this.getIntroductoryInstrumentAvailability();
      this.getLeaveBalance(this.selectedInstructorViewDetails?.email!);
    }
  }

  resetFiltersToToday(): void {
    const today = this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd) ?? '';
    this.filters.startDate = today;
    this.filters.endDate = today;
  }

  getAllLocations(): void {
    this.authService
      .getAllLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  loadMessages(): void {
    this.chatService
      .add(
        {
          page: this.currentPage,
          pageSize: this.pageSize,
          userEmail: this.selectedInstructorViewDetails?.email
        },
        API_URL.octopusChatAppServices.chatHistory
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<ChatHistoryRes>) => {
          this.chatHistory = res.result.items.map((message: ChatHistoryRes) => ({
            ...message,
            localLatestMessageTime: DateUtils.toLocal(message.lastMessageTime, 'yyyy-MM-DDTHH:mm:ss.SSS+0000', 'yyyy-MM-DDTHH:mm:ss')}));
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  @Debounce(300)
  getDependentSchedule(): void {
    this.showScheduleLoader = true;
    this.schedulerService
      .add(this.getFilterParams(this.filters.startDate, this.filters.endDate), API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<Array<ScheduleDetailsView>>) => {
          this.events = res.result;
          this.showPageLoader = false;
          this.showScheduleLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.showScheduleLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParams(minDate: string, maxDate: string): ScheduleFilters {
    const minScheduleDateFilter = this.datePipe.transform(minDate, this.constants.dateFormats.yyyy_MM_dd);
    const maxScheduleDateFilter = this.datePipe.transform(maxDate, this.constants.dateFormats.yyyy_MM_dd);

    return {
      minScheduleDateFilter: minScheduleDateFilter,
      maxScheduleDateFilter: maxScheduleDateFilter,
      locationIdFilter: [],
      instructorIdFilter: [this.selectedInstructorViewDetails?.id!],
      classTypeFilter: [],
      instrumentIdFilter: [],
      isNotShowDraftSchedule: this.currentUser$?.userRoleId === this.constants.roleIds.INSTRUCTOR || this.currentUser$?.userRoleId === this.constants.roleIds.SUPERVISOR ? true : false,
      ...this.appliedAdvanceFilter
    };
  }

  getTimeDiff(start: string, end: string): number | null {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  toggleMessageSideNav(isOpen: boolean): void {
    this.isMessageSideNavOpen = isOpen;
    this.selectedIdEmail = isOpen ? { email: this.selectedInstructorViewDetails?.email } : null;
  }

  getIntroductoryInstrumentAvailability(): void {
    this.noIntroductoryInstrumentAvailable = this.selectedInstructorViewDetails?.instruments?.every(
      instrument => !instrument.isIntroductoryClassAvailable
    );
  }

  updateDay(daysToAdd: number): void {
    const date = moment(this.filters.startDate).add(daysToAdd, 'days');

    this.filters.startDate = this.datePipe.transform(date.toDate(), this.constants.dateFormats.yyyy_MM_dd) ?? '';
    this.filters.endDate = this.datePipe.transform(date.toDate(), this.constants.dateFormats.yyyy_MM_dd) ?? '';
    this.getDependentSchedule();
  }

  onEdit(): void {
    this.openEditSideNav.emit();
  }

  getInitials(name?: string): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }

  getAvailabilityTypeName(value: number): string {
    return AvailabilityType[value];
  }

  getLeaveBalance(email: string): void {
    this.leaveRequestService
      .getList<CBGetResponse<LeaveBalance[]>>(`${API_URL.leaveManagement.getLeaveBalance}?UserEmail=${email}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<LeaveBalance[]>) => {
          this.leaveBalance = res.result;
          this.totalLeaves = this.leaveBalance[0].totalLeaveDays;
          this.usedLeaves = this.leaveBalance[0].usedLeaveDays;
          this.availableLeaves = this.leaveBalance[0].remainingLeaveDays;
          this.chartOptions = CommonUtils.getChartOptions(this.availableLeaves, this.usedLeaves, this.totalLeaves);
          this.cdr.detectChanges();
        }
      });
  }

  toggleLocationCollapse(): void {
    this.isLocationCollapsed = !this.isLocationCollapsed;
  }

  closeViewSideNavFun(): void {
    this.isLocationCollapsed = true;
    this.isBioCollapsed = true;
    this.closeViewSideNav.emit();
  }
}
