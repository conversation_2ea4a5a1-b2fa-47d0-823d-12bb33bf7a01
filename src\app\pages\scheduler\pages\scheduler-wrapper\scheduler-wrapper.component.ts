import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { SharedModule } from 'src/app/shared/shared.module';
import { ScheduleComponent } from './pages/schedule/schedule.component';
import { RoomScheduleComponent } from './pages/room-schedule/room-schedule.component';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { ActivatedRoute, Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatSidenavModule } from '@angular/material/sidenav';
import { AddScheduleComponent } from './pages/schedule/pages/add-schedule/add-schedule.component';
import { AddRoomScheduleComponent } from './pages/room-schedule/pages/add-room-schedule/add-room-schedule.component';
import { RoomSchedule } from './pages/room-schedule/models';
import { BulkCancelComponent } from './pages/schedule/pages/bulk-cancel/bulk-cancel.component';
import { takeUntil } from 'rxjs';
import { Account } from 'src/app/auth/models/user.model';
import { AuthService } from 'src/app/auth/services';

const DEPENDENCIES = {
  MODULE: [CommonModule, SharedModule, MatButtonModule, MatIconModule, MatSidenavModule],
  COMPONENT: [ScheduleComponent, RoomScheduleComponent, AddScheduleComponent, AddRoomScheduleComponent, BulkCancelComponent]
};

@Component({
  selector: 'app-scheduler-wrapper',
  standalone: true,
  imports: [...DEPENDENCIES.MODULE, ...DEPENDENCIES.COMPONENT],
  templateUrl: './scheduler-wrapper.component.html',
  styleUrl: './scheduler-wrapper.component.scss'
})
export class SchedulerWrapperComponent extends BaseComponent implements OnInit {
  @ViewChild(ScheduleComponent) scheduleComponent!: ScheduleComponent;
  @ViewChild(RoomScheduleComponent) roomScheduleComponent!: RoomScheduleComponent;

  selectedRoom!: RoomSchedule | null;
  isAddScheduleOpen!: boolean;
  isBulkCancelOpen = false;
  pageTabOptions = {
    SCHEDULE: 'Schedule',
    ROOM_SCHEDULE: 'Room Schedule'
  };
  selectedTabOption = this.pageTabOptions.SCHEDULE;
  scheduleDate = new Date();

  constructor(private readonly router: Router, private readonly activatedRoute: ActivatedRoute, private readonly authService: AuthService, private readonly cdr: ChangeDetectorRef) {
    super();
  }

  ngOnInit(): void {
    this.setActiveTabFromQueryParams();
    this.getCurrentUser();
  }

  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      this.selectedTabOption = params.activeTab ?? this.pageTabOptions.SCHEDULE;
    });
  }

  getCurrentUser(): void {
    this.authService
      .getCurrentUser$()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Account | null) => {
          this.currentUser = res;
          this.cdr.detectChanges();
        }
      });
  }

  asIsOrder() {
    return 1;
  }

  setActiveTabOption(tabName: string): void {
    this.selectedTabOption = tabName;
    this.router.navigate([this.path.schedule.root], {
      queryParams: {
        activeTab: tabName
      }
    });
  }

  toggleRoomSideNav(roomData: RoomSchedule | null, isOpen: boolean): void {
    this.isAddScheduleOpen = isOpen;
    this.selectedRoom = roomData;
  }

  updateScheduleData(): void {
    this.scheduleComponent.setDateToCurrentDate(false);
  }

  updateRoomData(): void {
    this.roomScheduleComponent.setDateToCurrentDate(false);
  }
}
