import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ScheduleDayViewComponent } from './pages/schedule-day-view/schedule-day-view.component';
import { MbscCalendarEvent, MbscResource } from '@mobiscroll/angular';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule, DatePipe } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { ScheduleListViewComponent } from './pages/schedule-list-view/schedule-list-view.component';
import { ScheduleWeekViewComponent } from './pages/schedule-week-view/schedule-week-view.component';
import { SchedulerService } from './services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import { SharedModule } from 'src/app/shared/shared.module';
import { InstructorList } from 'src/app/schedule-introductory-lesson/models';
import { AdvancedFilters, Filters, ClassTypes, ScheduleFilters, ScheduleDetailsView, FilterItem } from './models';
import { OverlayModule } from '@angular/cdk/overlay';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MultiSelectComponent } from 'src/app/shared/components/multi-select/multi-select.component';
import { Instrument } from 'src/app/request-information/models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ScheduleAdvanceFilterComponent } from './pages/schedule-advance-filter/schedule-advance-filter.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { UpdateScheduleComponent } from './pages/update-schedule/update-schedule.component';
import { Debounce } from 'src/app/shared/decorators';
import { CommonService } from 'src/app/shared/services';
import { AuthService } from 'src/app/auth/services';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { CommonUtils } from 'src/app/shared/utils';
import { SupervisorFilter } from 'src/app/pages/members/pages/supervisors/models';
import { ActivatedRoute, Router } from '@angular/router';

const DEPENDENCIES = {
  MODULES: [
    MatIconModule,
    CommonModule,
    MatFormFieldModule,
    MatSelectModule,
    FormsModule,
    SharedModule,
    OverlayModule,
    FormsModule,
    MatCheckboxModule,
    MatSidenavModule,
    MatDatepickerModule,
    MatInputModule
  ],
  COMPONENTS: [
    ScheduleDayViewComponent,
    ScheduleListViewComponent,
    ScheduleWeekViewComponent,
    MultiSelectComponent,
    ScheduleAdvanceFilterComponent,
    UpdateScheduleComponent
  ]
};

@Component({
  selector: 'app-schedule',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './schedule.component.html',
  styleUrl: './schedule.component.scss'
})
export class ScheduleComponent extends BaseComponent implements OnInit {
  schedulerData: MbscCalendarEvent[] = [];
  resources!: Array<MbscResource>;

  showScheduleForDate = new Date();
  firstDateOfCurrentWeek!: Date;
  lastDateOfCurrentWeek!: Date;
  isLoading!: boolean;
  isAdvanceFilterOpen!: boolean;
  isUpdateSchedule!: boolean;
  selectedEvent!: ScheduleDetailsView;
  appliedAdvanceFilter = new AdvancedFilters();
  originalAdvanceFIlters = new AdvancedFilters();
  locationFilterParam!: FilterItem;

  @Output() scheduleDate = new EventEmitter<Date>();

  filterParams: Filters = {
    location: {
      id: 1,
      defaultPlaceholder: 'All Locations',
      placeholder: 'All Locations',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: false,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    instructor: {
      id: 2,
      defaultPlaceholder: 'All Instructors',
      placeholder: 'All Instructors',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    instrument: {
      id: 3,
      placeholder: 'All Instruments',
      defaultPlaceholder: 'All Instruments',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: false,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    classType: {
      id: 4,
      placeholder: 'All Class',
      defaultPlaceholder: 'All Class',
      value: new Set([1, 2, 3, 4, 5, 6]),
      totalCount: 6,
      isOpen: false,
      showSearchBar: false,
      showClassBorder: true,
      options: [
        {
          id: ClassTypes.INTRODUCTORY,
          name: 'Introductory',
          image: this.constants.staticImages.icons.introductoryLessonOptionBorder
        },
        {
          id: ClassTypes.RECURRING,
          name: 'Recurring'
        },
        {
          id: ClassTypes.GROUP_CLASS,
          name: 'Group Class',
          image: this.constants.staticImages.icons.groupClassOptionLessonBorder
        },
        {
          id: ClassTypes.ENSEMBLE_CLASS,
          name: 'Ensemble Class',
          image: this.constants.staticImages.icons.ensembleClassOptionLessonBorder
        },
        {
          id: ClassTypes.SUMMER_CAMP,
          name: 'Summer Camp',
          image: this.constants.staticImages.icons.summerCampOptionLessonBorder
        },
        {
          id: ClassTypes.MAKE_UP,
          name: 'Make-Up Lesson',
          image: this.constants.staticImages.icons.makeUpLessonOptionLessonBorder
        }
      ] as Array<IdNameModel>
    }
  };

  schedulerViews = this.constants.schedulerViews;
  selectedScheduleView = this.schedulerViews.Day;
  data = {
    scheduleLessonDetails: [
      {
        classType: 2,
        lessonType: 2,
        studentDetails: [
          {
            studentId: 3,
            studentName: 'Max Ford',
            studentAge: 23,
            accountManagerName: null,
            accountManagerEmail: null,
            accountManagerPhoneNo: null,
            grade: null,
            isPresent: null,
            isPaid: false,
            planId: null,
            planName: null
          }
        ],
        locationId: 1,
        locationName: 'Octopus Music School North Brunswick',
        instrumentId: 3,
        instrumentName: 'Guitar',
        instructorId: 71,
        instructorName: 'Mark R',
        scheduleDate: '2025-07-14T00:00:00',
        start: '2025-07-14T14:30:00',
        end: '2025-07-14T15:00:00',
        roomId: 1,
        roomName: 'NB 1',
        instrumentColor: '#EFE4E8',
        skillType: 'Beginner',
        instrumentFontColor: '#944E63',
        isCancelSchedule: false,
        isSpecialNeedsLesson: false,
        campName: null,
        groupClassName: null,
        groupClassStartDate: null,
        groupClassEndDate: null,
        campStartDate: null,
        campEndDate: null,
        isDraftSchedule: false,
        recurringScheduleId: 3,
        isPaid: false,
        revenueCategoryId: null,
        revenueCategoryName: null,
        ensembleClassName: null,
        ensembleClassStartDate: null,
        ensembleClassEndDate: null,
        assignedInstructors: [],
        assignedInstruments: [],
        resource: 71,
        id: 428
      },
      {
        classType: 2,
        lessonType: 1,
        studentDetails: [
          {
            studentId: 57,
            studentName: 'Aarav Sharma',
            studentAge: 22,
            accountManagerName: null,
            accountManagerEmail: null,
            accountManagerPhoneNo: null,
            grade: null,
            isPresent: null,
            isPaid: false,
            planId: null,
            planName: null
          }
        ],
        locationId: 1,
        locationName: 'Octopus Music School North Brunswick',
        instrumentId: 1,
        instrumentName: 'Piano',
        instructorId: 114,
        instructorName: 'Piano Beginner',
        scheduleDate: '2025-06-04T00:00:00',
        start: '2025-06-04T15:30:00',
        end: '2025-06-04T16:00:00',
        roomId: 1,
        roomName: 'NB 1',
        instrumentColor: '#FFE1F8',
        skillType: 'Beginner',
        instrumentFontColor: '#FD35D1',
        isCancelSchedule: false,
        isSpecialNeedsLesson: false,
        campName: null,
        groupClassName: null,
        groupClassStartDate: null,
        groupClassEndDate: null,
        campStartDate: null,
        campEndDate: null,
        isDraftSchedule: false,
        recurringScheduleId: 59,
        isPaid: false,
        revenueCategoryId: null,
        revenueCategoryName: null,
        ensembleClassName: null,
        ensembleClassStartDate: null,
        ensembleClassEndDate: null,
        assignedInstructors: [],
        assignedInstruments: [],
        resource: 114,
        id: 4540
      },
      {
        classType: 2,
        lessonType: 1,
        studentDetails: [
          {
            studentId: 57,
            studentName: 'Aarav Sharma',
            studentAge: 22,
            accountManagerName: null,
            accountManagerEmail: null,
            accountManagerPhoneNo: null,
            grade: null,
            isPresent: null,
            isPaid: false,
            planId: null,
            planName: null
          }
        ],
        locationId: 1,
        locationName: 'Octopus Music School North Brunswick',
        instrumentId: 2,
        instrumentName: 'Voice',
        instructorId: 114,
        instructorName: 'Piano Beginner',
        scheduleDate: '2025-06-05T00:00:00',
        start: '2025-06-05T16:00:00',
        end: '2025-06-05T16:30:00',
        roomId: 1,
        roomName: 'NB 1',
        instrumentColor: '#D9E1F0',
        skillType: 'Beginner',
        instrumentFontColor: '#013499',
        isCancelSchedule: false,
        isSpecialNeedsLesson: false,
        campName: null,
        groupClassName: null,
        groupClassStartDate: null,
        groupClassEndDate: null,
        campStartDate: null,
        campEndDate: null,
        isDraftSchedule: false,
        recurringScheduleId: 64,
        isPaid: false,
        revenueCategoryId: null,
        revenueCategoryName: null,
        ensembleClassName: null,
        ensembleClassStartDate: null,
        ensembleClassEndDate: null,
        assignedInstructors: [],
        assignedInstruments: [],
        resource: 114,
        id: 4749
      }
    ],
    leavesDetails: [
      {
        memberRequestId: 73,
        instructorId: 102,
        name: 'kuro sense',
        profilePicture: null,
        leaveDate: '2025-06-01T00:00:00',
        leaveStartTime: '2025-06-01T18:00:00',
        leaveEndTime: '2025-06-01T20:00:00',
        leaveDuration: 0,
        leaveType: 1,
        reason: 'Going out',
        remark: null,
        approverId: 96,
        approverName: 'Desk Manager',
        availableDays: null,
        locationId: 2,
        resource: 0
      },
      {
        memberRequestId: 73,
        instructorId: 102,
        name: 'kuro sense',
        profilePicture: null,
        leaveDate: '2025-06-01T00:00:00',
        leaveStartTime: '2025-06-01T18:00:00',
        leaveEndTime: '2025-06-01T20:00:00',
        leaveDuration: 0,
        leaveType: 1,
        reason: 'Going out',
        remark: null,
        approverId: 96,
        approverName: 'Desk Manager',
        availableDays: null,
        locationId: 1,
        resource: 0
      },
      {
        memberRequestId: 76,
        instructorId: 102,
        name: 'kuro sense',
        profilePicture: null,
        leaveDate: '2025-06-03T00:00:00',
        leaveStartTime: '2025-05-28T12:30:00',
        leaveEndTime: '2025-05-28T15:00:00',
        leaveDuration: 0.5,
        leaveType: 2,
        reason: 'New partail',
        remark: null,
        approverId: 0,
        approverName: '',
        availableDays: null,
        locationId: 2,
        resource: 0
      },
      {
        memberRequestId: 76,
        instructorId: 102,
        name: 'kuro sense',
        profilePicture: null,
        leaveDate: '2025-06-03T00:00:00',
        leaveStartTime: '2025-05-28T12:30:00',
        leaveEndTime: '2025-05-28T15:00:00',
        leaveDuration: 0.5,
        leaveType: 2,
        reason: 'New partail',
        remark: null,
        approverId: 0,
        approverName: '',
        availableDays: null,
        locationId: 1,
        resource: 0
      },
      {
        memberRequestId: 83,
        instructorId: 4,
        name: 'Albert M',
        profilePicture: '',
        leaveDate: '2025-06-01T00:00:00',
        leaveStartTime: null,
        leaveEndTime: null,
        leaveDuration: 1,
        leaveType: 2,
        reason: 'Family emergency',
        remark: 'test',
        approverId: 6,
        approverName: 'Albert M',
        availableDays: null,
        locationId: 2,
        resource: 0
      },
      {
        memberRequestId: 83,
        instructorId: 4,
        name: 'Albert M',
        profilePicture: '',
        leaveDate: '2025-06-01T00:00:00',
        leaveStartTime: null,
        leaveEndTime: null,
        leaveDuration: 1,
        leaveType: 2,
        reason: 'Family emergency',
        remark: 'test',
        approverId: 6,
        approverName: 'Albert M',
        availableDays: null,
        locationId: 2,
        resource: 0
      },
      {
        memberRequestId: 83,
        instructorId: 4,
        name: 'Albert M',
        profilePicture: '',
        leaveDate: '2025-06-01T00:00:00',
        leaveStartTime: null,
        leaveEndTime: null,
        leaveDuration: 1,
        leaveType: 2,
        reason: 'Family emergency',
        remark: 'test',
        approverId: 6,
        approverName: 'Albert M',
        availableDays: null,
        locationId: 2,
        resource: 0
      },
      {
        memberRequestId: 85,
        instructorId: 103,
        name: 'Octopus Instructor',
        profilePicture: null,
        leaveDate: '2025-06-03T00:00:00',
        leaveStartTime: '2025-06-03T16:00:00',
        leaveEndTime: '2025-06-03T17:00:00',
        leaveDuration: 0.25,
        leaveType: 2,
        reason: 'New Test',
        remark: null,
        approverId: 0,
        approverName: '',
        availableDays: null,
        locationId: 1,
        resource: 0
      }
    ]
  };

  constructor(
    private readonly schedulerService: SchedulerService,
    private readonly datePipe: DatePipe,
    private readonly cdr: ChangeDetectorRef,
    private readonly commonService: CommonService,
    private readonly authService: AuthService,
    private readonly instructorService: InstructorService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
    this.getScheduleDateFromQueryParams();
    this.getFiltersListData();
  }

  getScheduleDateFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((queryParams: any) => {
      if (queryParams.date) {
        this.showScheduleForDate = new Date(queryParams.date);
        this.scheduleDate.emit(this.showScheduleForDate);
        this.removeQueryParams();
      }
    });
  }

  removeQueryParams(): void {
    this.router.navigate([], { queryParams: {}, queryParamsHandling: null });
  }

  initGetScheduleData(minDate: Date, maxDate: Date): void {
    this.isLoading = true;
    this.getSchedule(minDate, maxDate);
  }

  @Debounce(300)
  getSchedule(minDate: Date, maxDate: Date): void {
    this.schedulerService
      .add(this.getFilterParams(minDate, maxDate), API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          // Handle new nested API response structure
          if (res.result) {
            res.result.scheduleLessonDetails = this.data.scheduleLessonDetails;
            res.result.leavesDetails = this.data.leavesDetails;

            // Process schedule lesson details
            const scheduleEvents = res.result.scheduleLessonDetails
              ? res.result.scheduleLessonDetails.map((event: MbscCalendarEvent) => {
                  return {
                    ...event,
                    resource: event?.['instructorId'] || event?.['assignedInstructors']?.map((instructor: any) => instructor.instructorId)
                  };
                })
              : [];

            // Process leave details and convert them to calendar events
            const leaveEvents = res.result.leavesDetails
              ? res.result.leavesDetails.map((leave: any) => {
                  return {
                    ...leave,
                    // Map leave properties to calendar event format
                    start: leave.leaveStartTime || leave.leaveDate,
                    end: leave.leaveEndTime || leave.leaveDate,
                    title: `${leave.name} - ${leave.reason}`,
                    color: '#ff9999', // Light red color for leaves
                    resource: leave.instructorId,
                    isLeave: true, // Flag to identify leave events
                    id: `leave_${leave.memberRequestId}_${leave.instructorId}` // Unique ID for leave events
                  };
                })
              : [];

            // Combine schedule and leave events
            this.schedulerData = [...scheduleEvents, ...leaveEvents];
          }

          // Comment out original logic for reference
          // if (res.result) {
          //   this.schedulerData = res.result.map((event: MbscCalendarEvent) => {
          //     return { ...event, resource: event?.['instructorId'] || event?.['assignedInstructors'].map((instructor: any) => instructor.instructorId) };
          //   });
          // }

          this.locationFilterParam = { ...this.filterParams.location };
          this.resources = this.filterParams.instructor.value.size
            ? this.filterParams.instructor.options.filter((instructor: IdNameModel) =>
                this.filterParams.instructor.value.has(instructor.id)
              )
            : this.filterParams.instructor.options;
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstructorFilter(): Array<number> {
    switch (this.currentUser?.userRoleId) {
      case this.constants.roleIds.INSTRUCTOR:
        return [this.currentUser?.dependentId];
      case this.constants.roleIds.SUPERVISOR:
        return this.filterParams.instructor.value ? [...Array.from(this.filterParams.instructor.value), this.currentUser?.dependentId] : [];
      default:
        return this.filterParams.instructor.value ? Array.from(this.filterParams.instructor.value) : [];
    }
  }

  getFilterParams(minDate: Date, maxDate: Date): ScheduleFilters {
    const minScheduleDateFilter = this.datePipe.transform(minDate, this.constants.dateFormats.yyyy_MM_dd);
    const maxScheduleDateFilter = this.datePipe.transform(maxDate, this.constants.dateFormats.yyyy_MM_dd);
    const instrumentIdFilter = Array.from(this.filterParams.instrument.value);
    const instructorIdFilter = this.getInstructorFilter();

    if (instrumentIdFilter.length > 0) {
      instrumentIdFilter.push(0);
    }

    return {
      minScheduleDateFilter: minScheduleDateFilter,
      maxScheduleDateFilter: maxScheduleDateFilter,
      locationIdFilter: Array.from(this.filterParams.location.value),
      instructorIdFilter: instructorIdFilter,
      classTypeFilter: Array.from(this.filterParams.classType.value),
      instrumentIdFilter: instrumentIdFilter,
      isNotShowDraftSchedule:
        this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR ||
        this.currentUser?.userRoleId === this.constants.roleIds.SUPERVISOR
          ? true
          : false,
      ...this.appliedAdvanceFilter
    };
  }

  getFiltersListData(): void {
    this.getLocations();
    this.getInstruments();
  }

  getFilterParamsForInstructors() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      SupervisorIdFilter: this.currentUser?.isSupervisor ? this.currentUser?.dependentId : null,
      isSupervisorFilter: SupervisorFilter.ALL,
      Page: 1
    });
  }

  getInstructorsWithSupervisor(): void {
    this.instructorService
      .add(this.getFilterParamsForInstructors(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<InstructorList>) => {
          const items = res.result.items;
          const instructor = items.find(item => item.instructorDetail.id === this.currentUser?.dependentId) || {
            instructorDetail: { id: 0, name: '' }
          };
          this.filterParams.instructor.options =
            this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR
              ? [{ id: instructor.instructorDetail.id, name: instructor.instructorDetail.name }]
              : items.map(item => ({
                  ...item.instructorDetail
                }));

          this.filterParams.instructor.totalCount = items.length;
          this.filterParams.instructor.value = new Set(this.filterParams.instructor.options.map(option => option.id));
          this.setLocationInDayView();
          this.initGetScheduleData(this.showScheduleForDate, this.showScheduleForDate);
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          const { items } = res.result;
          this.filterParams.instrument.options = items.map(item => ({
            id: item.instrumentDetail.id,
            name: item.instrumentDetail.name
          }));
          this.filterParams.instrument.value = new Set(items.map(item => item.instrumentDetail.id));
          this.filterParams.instrument.totalCount = items.length;
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          const { items } = res.result;
          this.filterParams.location.options = items.map(item => ({
            id: item.schoolLocations.id,
            name: item.schoolLocations.locationName
          }));
          this.filterParams.location.value = new Set(items.map(item => item.schoolLocations.id));
          this.filterParams.location.totalCount = items.length;
          this.cdr.detectChanges();
        }
      });
  }

  getCurrentUser(): void {
    this.isLoading = true;
    this.authService
      .getCurrentUser$()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.getInstructorsWithSupervisor();
          this.cdr.detectChanges();
        }
      });
  }

  applyAdvanceFilters(advanceFilters: AdvancedFilters): void {
    this.appliedAdvanceFilter = advanceFilters;
    this.setDateToCurrentDate(false);
    this.isAdvanceFilterOpen = false;
  }

  openAdvanceFilterAndStoreInitialFilters(): void {
    this.originalAdvanceFIlters = JSON.parse(JSON.stringify(this.appliedAdvanceFilter));
    this.isAdvanceFilterOpen = true;
  }

  closeAdvanceFilterAndRestoreData(): void {
    this.appliedAdvanceFilter = this.originalAdvanceFIlters;
    this.isAdvanceFilterOpen = false;
  }

  openScheduleUpdateModel(event: ScheduleDetailsView): void {
    this.selectedEvent = event;
    this.isUpdateSchedule = true;
  }

  removeSelectedLocation(): void {
    const selectedValues = Array.from(this.filterParams.location.value);
    const firstSelectedValueName = this.filterParams.location.options.find(option => option.id === selectedValues[0])?.name;
    const additionalCount = selectedValues.length > 1 ? `+${selectedValues.length - 1}` : '';
    this.filterParams.location.placeholder = ` ${firstSelectedValueName} ${additionalCount}`;
    this.setDateToCurrentDate(false);
  }

  goToPreviousDate(): void {
    const previousDate = new Date(this.showScheduleForDate);
    switch (this.selectedScheduleView) {
      case this.schedulerViews.Week:
        previousDate.setDate(this.showScheduleForDate.getDate() - 7);
        this.setFirstAndLastDateOfCurrentWeek(previousDate);
        break;
      default:
        previousDate.setDate(this.showScheduleForDate.getDate() - 1);
        this.initGetScheduleData(previousDate, previousDate);
        break;
    }
    this.showScheduleForDate = previousDate;
    this.scheduleDate.emit(this.showScheduleForDate);
  }

  goToNextDate(): void {
    this.isLoading = true;
    const nextDate = new Date(this.showScheduleForDate);
    switch (this.selectedScheduleView) {
      case this.schedulerViews.Week:
        nextDate.setDate(this.showScheduleForDate.getDate() + 7);
        this.setFirstAndLastDateOfCurrentWeek(nextDate);
        break;
      default:
        nextDate.setDate(this.showScheduleForDate.getDate() + 1);
        this.initGetScheduleData(nextDate, nextDate);
        break;
    }
    this.showScheduleForDate = nextDate;
    this.scheduleDate.emit(this.showScheduleForDate);
  }

  setLocationInDayView(): void {
    if (this.selectedScheduleView === this.schedulerViews.Day) {
      if (this.filterParams.location.options.length >= 2) {
        const firstSelectedValueName = this.filterParams.location.options[0].name;
        this.filterParams.location.placeholder = ` ${firstSelectedValueName} +1`;
        this.filterParams.location.value = new Set([this.filterParams.location.options[0].id, this.filterParams.location.options[1].id]);
      }
      return;
    }
  }

  setDateToCurrentDate(currentDate = true): void {
    this.showScheduleForDate = currentDate ? new Date() : this.showScheduleForDate;
    this.scheduleDate.emit(this.showScheduleForDate);
    switch (this.selectedScheduleView) {
      case this.schedulerViews.Week:
        this.setFirstAndLastDateOfCurrentWeek(this.showScheduleForDate);
        break;
      default:
        this.initGetScheduleData(this.showScheduleForDate, this.showScheduleForDate);
        break;
    }
  }

  onCalendarDateChange(date: Date): void {
    this.showScheduleForDate = date;
    this.setDateToCurrentDate(false);
    this.scheduleDate.emit(this.showScheduleForDate);
  }

  setFirstAndLastDateOfCurrentWeek(currentDate: Date): void {
    this.firstDateOfCurrentWeek = this.getFirstDateOfCurrentWeek(currentDate);
    this.lastDateOfCurrentWeek = new Date(this.firstDateOfCurrentWeek);
    this.lastDateOfCurrentWeek.setDate(this.firstDateOfCurrentWeek.getDate() + 6);
    this.initGetScheduleData(this.firstDateOfCurrentWeek, this.lastDateOfCurrentWeek);
  }

  getFirstDateOfCurrentWeek(currentDate: Date): Date {
    const date = new Date(currentDate);
    const dayOfWeek = date.getDay();
    const diff = date.getDate() - dayOfWeek;
    date.setDate(diff);
    date.setHours(0, 0, 0, 0);
    return date;
  }

  asIsOrder() {
    return 1;
  }
}
