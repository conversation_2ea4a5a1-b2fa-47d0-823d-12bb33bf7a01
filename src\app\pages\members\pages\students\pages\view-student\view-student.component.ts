import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule, DatePipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { CommonUtils } from 'src/app/shared/utils';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { takeUntil } from 'rxjs';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { DashIfEmptyPipe, LocalDatePipe } from 'src/app/shared/pipe';
import {
  DependentInformationParams,
  DependentInformations,
  IdEmailModel,
  MakeUpPassParams,
  StudentGradeFormGroup,
  StudentGrades
} from '../../models';
import { StudentPlanService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { ClassTypes, StudentPlans } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { Plan } from 'src/app/pages/settings/pages/plan/models';
import { DependentService } from 'src/app/pages/profile/services';
import moment from 'moment';
import { CurrentUserScheduleLessonDetail } from 'src/app/pages/visits-scheduling/models';
import { StudentGradeService } from '../../services';
import { SignedDocumentService } from 'src/app/pages/user-document/services';
import { SignedDocuments } from 'src/app/pages/user-document/models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ViewStudentDocumentsComponent } from '../view-student-documents/view-student-documents.component';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Instrument } from 'src/app/request-information/models';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { GradeLevel } from '../../../instructors/models';
import { AuthService } from 'src/app/auth/services';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { SignUpForOptions } from 'src/app/auth/models';
import { Debounce } from 'src/app/shared/decorators';
import { AddScheduleComponent } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/pages/add-schedule/add-schedule.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { PassesService } from 'src/app/pages/settings/pages/passes/services';
import { AddStudentComponent } from '../add-student/add-student.component';
import { Account } from 'src/app/auth/models/user.model';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AssignedInstructors } from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { ChatHistoryRes, ChatMessageType, FileType } from 'src/app/pages/messages/models';
import { ChatService } from 'src/app/pages/messages/services';
import { MessagesComponent } from '../../../../../messages/messages.component';
import { PaymentMethodsComponent } from 'src/app/shared/components/payment-methods/payment-methods.component';
import { AllAchOfUser, AllCardsOfUser, AllCustomerCards } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { PaymentService } from 'src/app/pages/schedule-classes/services';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    MatButtonModule,
    SharedModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatSidenavModule,
    ReactiveFormsModule,
    MatTooltipModule
  ],
  PIPES: [DashIfEmptyPipe, LocalDatePipe],
  COMPONENTS: [ViewStudentDocumentsComponent, AddScheduleComponent, AddStudentComponent, MessagesComponent, PaymentMethodsComponent]
};

@Component({
  selector: 'app-view-student',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './view-student.component.html',
  styleUrl: './view-student.component.scss'
})
export class ViewStudentComponent extends BaseComponent implements OnChanges {
  @Input() selectedStudentDetails!: DependentInformations | undefined;
  @Input() isFromAttendance = false;

  showAllInstructor = false;
  showScheduleLoader = false;
  showGradeLoader = false;
  isViewDocumentSideNavOpen = false;
  isUpdateGrade = false;
  showSupervisorSchedule = false;
  showAccountManagerDetails = false;
  isScheduleALesson = false;
  showEnsembleLoader = false;
  isEnsembleAvailable = false;
  showAllSchedule = false;
  showAllPlans = false;
  showMessageLoader = false;
  isMessageSideNavOpen = false;
  isPaymentMethodSideNavOpen = false;
  showPaymentMethodLoader = false;

  plans = Plan;
  classTypes = ClassTypes;
  chatTypes = ChatMessageType;
  fileTypes = FileType;

  selectedStudentId!: number | undefined;
  selectedIdEmail!: IdEmailModel | null;
  ensemblePlans!: Array<StudentPlans>;
  otherPlans!: Array<StudentPlans>;
  dependentSchedule!: Array<CurrentUserScheduleLessonDetail>;
  studentGrades!: Array<StudentGrades>;
  studentDocuments!: Array<SignedDocuments>;
  allCustomerCards!: AllCustomerCards;
  defaultCard!: AllCardsOfUser | undefined;
  defaultAch!: AllAchOfUser | undefined;
  gradeFormGroup!: FormGroup<StudentGradeFormGroup>;
  instruments!: Array<Instrument>;
  gradeLevels!: Array<GradeLevel>;
  instructorIdsUnderSupervisor: Array<number> = [];
  userTypes = SignUpForOptions;
  isAddDependentSideNavOpen!: boolean;
  isEditAccountManagerSideNavOpen!: boolean;
  accManagerDetails?: Account;
  chatHistory: Array<ChatHistoryRes> = [];
  accountManagerUserTypes = SignUpForOptions;
  currentPage = this.paginationConfig.pageNumber;
  pageSize = this.paginationConfig.twoItemsPerPage;

  filters: DependentInformationParams = {
    startDate: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd) ?? '',
    endDate: this.datePipe.transform(moment().add(6, 'days').toDate(), this.constants.dateFormats.yyyy_MM_dd) ?? '',
    dependentId: 0
  };

  @Output() closeViewSideNav = new EventEmitter<void>();
  @Output() toggleAssignPlanAndProduct = new EventEmitter<StudentPlans[]>();
  @Output() openDependentDetails = new EventEmitter<number>();
  @Output() refreshStudentList = new EventEmitter<void>();

  constructor(
    private readonly cdr: ChangeDetectorRef,
    protected readonly planSummaryService: PlanSummaryService,
    private readonly studentPlanService: StudentPlanService,
    private readonly dependentService: DependentService,
    private readonly datePipe: DatePipe,
    private readonly studentGradeService: StudentGradeService,
    private readonly signedDocumentService: SignedDocumentService,
    private readonly commonService: CommonService,
    private readonly authService: AuthService,
    private readonly instructorService: InstructorService,
    private readonly dialog: MatDialog,
    private readonly passesService: PassesService,
    private readonly paymentService: PaymentService,
    private readonly toasterService: AppToasterService,
    private readonly chatService: ChatService
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.showPageLoader = true;
    if (changes['selectedStudentDetails']?.currentValue) {
      this.selectedStudentDetails = changes['selectedStudentDetails'].currentValue;
      this.getAccountManagerDetails();
      if (!(this.selectedStudentDetails?.accountManagerUserType == SignUpForOptions.YOUR_CHILD && this.selectedStudentDetails?.isAccountManager)) {
        this.getCurrentUser();
        this.getStudentPlans(this.selectedStudentDetails!.id);
        this.getStudentGrades(this.selectedStudentDetails!.id);
        this.getDocumentById(this.selectedStudentDetails!.id);
        this.getInstruments();
        this.getGradeLevels();
        this.loadMessages();
        this.initGradeForm();
      }
    }
  }

  initGradeForm(): void {
    this.gradeFormGroup = new FormGroup<StudentGradeFormGroup>({
      instrumentId: new FormControl(undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      studentId: new FormControl(this.selectedStudentDetails!.id, {
        nonNullable: true
      }),
      grade: new FormControl(undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      id: new FormControl(undefined, { nonNullable: true })
    });
  }

  getCurrentUser(): void {
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.getInstructorIdsUnderSupervisor();
          this.updateScheduleOption(false);
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getStudentPlans(studentId: number): void {
    this.studentPlanService
      .getList<CBResponse<StudentPlans>>(`${API_URL.studentPlans.getStudentPlans}?DependentInformationId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentPlans>) => {
          const allPlans = res.result.items;
          this.ensemblePlans = allPlans.filter(plan => plan.isEnsembleAvailable);
          this.otherPlans = allPlans.filter(plan => !plan.isEnsembleAvailable);
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getInstructorNames(array: AssignedInstructors[]) {
    return array
      .slice(1)
      .map(item => item.instructorName)
      .join(', ');
  }

  getStudentGrades(studentId: number): void {
    this.showGradeLoader = true;
    this.studentGradeService
      .getList<CBResponse<StudentGrades>>(`${API_URL.studentGrades.getAllByStudentId}?studentId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentGrades>) => {
          this.studentGrades = res.result.items;
          this.showGradeLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showGradeLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getDocumentById(studentId: number): void {
    this.signedDocumentService
      .getList<CBResponse<SignedDocuments>>(`${API_URL.crud.getAll}?StudentId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SignedDocuments>) => {
          this.studentDocuments = res.result.items;
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getGradeLevels(): void {
    this.gradeLevels = [];
    for (let i = 1; i <= 10; i++) {
      this.gradeLevels.push({ gradeLevel: { id: i, name: String(i) } });
    }
  }

  getAccountManagerDetails(): void {
    this.authService
      .getUserDetailsFromId(this.selectedStudentDetails?.accountManagerId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.accManagerDetails = res.result;
          if (this.selectedStudentDetails?.accountManagerUserType === SignUpForOptions.YOUR_CHILD && this.selectedStudentDetails?.isAccountManager) {
            this.setShowAccountManagerDetails(true);
          }
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  toggleMessageSideNav(isOpen: boolean): void {
    this.isMessageSideNavOpen = isOpen;
    this.selectedIdEmail = isOpen ? { id: this.selectedStudentDetails?.id, email: this.selectedStudentDetails?.accountManagerEmail } : null;
  }

  loadMessages(): void {
    this.showMessageLoader = true;
    this.chatService
      .add(
        {
          page: this.currentPage,
          pageSize: this.pageSize,
          studentId: this.selectedStudentDetails?.id,
          userEmail: this.selectedStudentDetails?.accountManagerEmail
        },
        API_URL.octopusChatAppServices.chatHistory
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<ChatHistoryRes>) => {
          this.chatHistory = res.result.items;
          this.showMessageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showMessageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getStudentAge(dob: string): number {
    return CommonUtils.getAgeFromDob(dob);
  }

  getFilterParams(instructorIds?: Array<number>) {
    const isInstructor = this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR;
    const instructorId = isInstructor || this.currentUser?.isSupervisor ? instructorIds : [];

    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      startDate: this.filters.startDate,
      endDate: this.filters.endDate,
      dependentId: this.selectedStudentDetails!.id,
      instructorIds: isInstructor || this.currentUser?.isSupervisor ? instructorId : [],
      isNotShowDraftSchedule: this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR || this.currentUser?.userRoleId === this.constants.roleIds.SUPERVISOR ? true : false
    });
  }

  @Debounce(300)
  getDependentSchedule(instructorIds?: Array<number>): void {
    this.showScheduleLoader = true;
    this.dependentService
      .add(this.getFilterParams(instructorIds), API_URL.dependentInformations.getDependentSchedule)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<CurrentUserScheduleLessonDetail>) => {
          this.dependentSchedule = res.result.items;
          this.showScheduleLoader = false;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showScheduleLoader = false;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  updateScheduleOption(selectedOption: boolean): void {
    this.showSupervisorSchedule = selectedOption;

    const instructorIds = selectedOption ? [...this.instructorIdsUnderSupervisor] : [this.currentUser?.dependentId!];

    this.getDependentSchedule(instructorIds);
    this.cdr.detectChanges();
  }

  getInstructorIdsUnderSupervisor(): void {
    if (this.currentUser?.isSupervisor) {
      this.instructorService
        .getList<CBGetResponse<IdNameModel[]>>(
          `${API_URL.instructorDetails.getInstructorUnderSupervisor}?supervisorId=${this.currentUser?.dependentId}`
        )
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<IdNameModel[]>) => {
            this.instructorIdsUnderSupervisor = response.result.map(item => item.id);
            this.cdr.detectChanges();
          }
        });
    }
  }

  updateWeek(weeksToAdd: number): void {
    const startDate = moment(this.filters.startDate).add(weeksToAdd, 'week');
    const endDate = moment(startDate).add(6, 'days');

    this.filters.startDate = this.datePipe.transform(startDate.toDate(), this.constants.dateFormats.yyyy_MM_dd) ?? '';
    this.filters.endDate = this.datePipe.transform(endDate.toDate(), this.constants.dateFormats.yyyy_MM_dd) ?? '';
    this.updateScheduleOption(this.showSupervisorSchedule);
  }

  onUpdateGrade(): void {
    const instrumentId = this.gradeFormGroup.getRawValue().instrumentId;
    const matchingGrade = this.studentGrades.find(studentGrade => studentGrade.studentGrade.instrumentId === instrumentId);
    this.gradeFormGroup.controls.id.setValue(matchingGrade ? matchingGrade.studentGrade.id : undefined);

    this.showBtnLoader = true;
    this.studentGradeService
      .add(this.gradeFormGroup.getRawValue(), API_URL.crud.createOrEdit)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.resetGradeForm();
          this.getStudentGrades(this.selectedStudentDetails!.id);
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  resetGradeForm(): void {
    this.isUpdateGrade = false;
    this.gradeFormGroup.reset();
  }

  openConfirmationPopup(scheduleDetail: CurrentUserScheduleLessonDetail): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Assign Make Up Pass',
        message: `Are you sure you want to assign ${scheduleDetail.instrumentName} Lesson make up pass to ${scheduleDetail.studentName}?`
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.onAssignMakeUpPass(scheduleDetail);
      }
    });
  }

  getMakeUpPassParams(scheduleDetail: CurrentUserScheduleLessonDetail): MakeUpPassParams {
    return { scheduleId: scheduleDetail.id, studentId: scheduleDetail.studentId, instrumentId: scheduleDetail.instrumentId };
  }

  onAssignMakeUpPass(scheduleDetail: CurrentUserScheduleLessonDetail): void {
    this.passesService.add(this.getMakeUpPassParams(scheduleDetail), API_URL.passes.createMakeUpLessonPass).subscribe({
      next: () => {
        this.getDependentSchedule();
        this.toasterService.success(this.constants.successMessages.assignedSuccessfully.replace('{item}', 'Make Up Pass'));
        this.cdr.detectChanges();
      }
    });
  }

  shouldShowDivider(totalItemsLength: number, index: number, showAll: boolean, minShow: number): boolean {
    if (index === totalItemsLength - 1) {
      return false;
    }

    if (!showAll) {
      return index < minShow - 1;
    }

    return true;
  }

  getInitials(firstName?: string, lastName?: string): string {
    return CommonUtils.getInitials(firstName, lastName);
  }

  getInitialsFromFullName(name?: string): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }

  openAssignPlanAndProduct(): void {
    this.toggleAssignPlanAndProduct.emit({ ...this.ensemblePlans, ...this.otherPlans });
  }

  onBookIntroductoryLesson() {
    this.isScheduleALesson = true;
    this.selectedStudentId = !this.showAccountManagerDetails
      ? this.selectedStudentDetails?.id
      : this.selectedStudentDetails?.accountManagerUserType === SignUpForOptions.YOUR_CHILD
      ? this.selectedStudentDetails?.accountManagerId
      : this.selectedStudentDetails?.accountManagerDependentId;
  }

  closeViewSideNavFun(): void {
    this.closeViewSideNav.emit();
  }

  onClickViewDependentDetails(studentId?: number): void {
    this.openDependentDetails.emit(studentId);
  }

  setShowAccountManagerDetails(showAccountManagerDetails: boolean): void {
    this.showAccountManagerDetails = showAccountManagerDetails;
  }

  onLessonSchedule(): void {
    this.isScheduleALesson = false;
    this.closeViewSideNavFun();
  }
}
