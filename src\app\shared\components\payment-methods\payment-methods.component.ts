import { ChangeDetectorRef, Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { MatRadioModule } from '@angular/material/radio';
import { MatIconModule } from '@angular/material/icon';
import { CardMethodComponent } from '../card-method/card-method.component';
import { Account } from 'src/app/auth/models/user.model';
import { AchDetailsComponent } from '../ach-details/ach-details.component';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { BaseComponent } from '../base-component/base.component';
import { API_URL } from '../../constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { DirectivesModule } from '../../directives/directives.module';
import { MatButtonModule } from '@angular/material/button';
import { AuthService } from 'src/app/auth/services';
import { CBGetResponse } from '../../models';
import { AllCardsOfUser, AllCustomerCards, PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { AppToasterService } from '../../services';
import { TransactionTypes } from 'src/app/pages/shop/models';

const DEPENDENCIES = {
  MODULES: [MatRadioModule, MatIconModule, FormsModule, CommonModule, DirectivesModule, MatButtonModule],
  COMPONENTS: [CardMethodComponent, AchDetailsComponent]
};

@Component({
  selector: 'app-payment-methods',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './payment-methods.component.html',
  styleUrl: './payment-methods.component.scss'
})
export class PaymentMethodsComponent extends BaseComponent {
  @Input() screen = '';
  @Input() showDefaultPaymentBtn = true;
  @Input() accManagerDetails!: Account | undefined;
  @Input() isPaymentFailed!: boolean;

  selectedMethod!: string;
  allCustomerCards: AllCustomerCards = { getAllCardsOfUser: [], getAllAchDetailsOfUser: [] };
  selectedCardDetail: PaymentParams = {};
  selectedPaymentDetails: PaymentParams = {};

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() sendSelectedPaymentDetail = new EventEmitter<PaymentParams>();

  constructor(
    private readonly paymentService: PaymentService,
    private readonly toasterService: AppToasterService,
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['accManagerDetails']?.currentValue) {
      this.accManagerDetails = changes['accManagerDetails'].currentValue;
      this.getAllCustomerCards();
    }
  }

  getCurrentUser(): void {
    if (this.accManagerDetails) {
      return;
    }
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.accManagerDetails = res as Account;
          this.getAllCustomerCards();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getAllCustomerCards(): void {
     if (this.screen !== 'billing-screen') {
      this.selectedMethod = this.constants.paymentMethod.Card;
    }
    this.paymentService
      .getList<CBGetResponse<AllCustomerCards>>(`${API_URL.payment.getAllCustomerCards}?userId=${this.accManagerDetails?.userId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<AllCustomerCards>) => {
          this.allCustomerCards = res.result;
          this.selectedMethod = this.selectedMethod
            ? this.selectedMethod
            : res.result?.getAllCardsOfUser.some((card: AllCardsOfUser) => card.isDefault)
            ? this.constants.paymentMethod.Card
            : this.constants.paymentMethod.ACH;
          this.selectedCardDetail = res.result?.getAllCardsOfUser.find((card: AllCardsOfUser) => card.isDefault) || ({} as PaymentParams);
          this.sendSelectedCardDetails(this.selectedCardDetail);
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  sendSelectedCardDetails(selectedCard?: PaymentParams): void {
    this.selectedCardDetail = selectedCard || this.selectedCardDetail;
    if (this.selectedMethod === this.constants.paymentMethod.Card) {
      this.selectedPaymentDetails = this.selectedCardDetail;
    }
    else if (this.selectedMethod === this.constants.paymentMethod.ACH && this.allCustomerCards?.getAllAchDetailsOfUser.length) {
      this.selectedPaymentDetails = this.allCustomerCards?.getAllAchDetailsOfUser[0];
    }
    this.sendSelectedPaymentDetail.emit({ ...this.selectedPaymentDetails, transactionType: this.selectedMethod === this.constants.paymentMethod.Card ? TransactionTypes.CARD : TransactionTypes.ACH });
    this.cdr.detectChanges();
  }

  get getSaveAsDefaultCard(): PaymentParams {
    return {
      userId: this.accManagerDetails?.userId,
      customerVaultId: this.selectedPaymentDetails.customerVaultId
    };
  }

  savedCardAsDefault(): void {
    if (!this.getSaveAsDefaultCard.customerVaultId) {
      this.toasterService.error(this.constants.errorMessages.noPaymentMethod);
      return;
    }
    this.showBtnLoader = true;
    this.paymentService.add(this.getSaveAsDefaultCard, API_URL.payment.setDefaultPaymentMethod).subscribe({
      next: () => {
        this.showBtnLoader = false;
        this.toasterService.success(this.constants.successMessages.setSuccessfully.replace('{item}', 'Default payment method'));
        this.getAllCustomerCards();
        this.cdr.detectChanges();
      },
      error: () => {
        this.showBtnLoader = false;
        this.cdr.detectChanges();
      }
    });
  }

  closeSideNavFn(): void {
    this.closeSideNav.emit();
  }
}
