<div class="list-view-wrapper" [ngClass]="{ 'no-header': currentUser$?.userRoleId === constants.roleIds.INSTRUCTOR || currentUser$?.userRoleId === constants.roleIds.SUPERVISOR }">
  <div class="current-date-event-count">
    <div>{{ showScheduleForDate | date : constants.fullDate }}</div>
    <div class="dot"></div>
    <div class="events" *ngIf="schedulerData">{{ schedulerData.length }} Events</div>
  </div>
  <div class="agenda-wrapper" *ngIf="schedulerData">
    <ng-container [ngTemplateOutlet]="isLoading ? showLoader : showListView"></ng-container>
  </div>
</div>

<mbsc-popup
  class="md-tooltip"
  #eventDetailsPopup
  [anchor]="detailsAnchor"
  [options]="popupOptions"
  (onClose)="schedulerDetailPopupComponent.showCancelLessonView = false"
>
  <app-scheduler-detail-popup
    [selectedEvent]="selectedEvent"
    (editLesson)="onEditLesson($event)"
    (closePopup)="closeEventDetailsPopup($event)"
  ></app-scheduler-detail-popup>
</mbsc-popup>

<ng-template #showListView>
  <mbsc-eventcalendar
    [options]="calendarOptions"
    [data]="schedulerData"
    themeVariant="light"
    [eventTemplate]="customEventTemplate"
    [selectedDate]="showScheduleForDate"
  >
    <ng-template #customEventTemplate let-data>
      <div
        (click)="openEventDetailsPopup(data.original, $event)"
        [ngStyle]="{
          background: schedulerService.getScheduleBackgroundColor(
            data.original.start,
            data.original.isDraftSchedule,
            data.original.instrumentColor,
            data.original.classType
          )
        }"
        class="schedule-item"
      >
        <div
          class="schedule-border"
          [ngStyle]="{
            'background-image': schedulerService.getScheduleBorderImage(
              data.original.start,
              data.original.isDraftSchedule,
              data.original.classType
            ),
            'background-color': schedulerService.getScheduleColor(
              data.original.start,
              data.original.isDraftSchedule,
              data.original.instrumentFontColor,
              data.original.classType
            )
          }"
        ></div>
        <div
          class="time-wrapper"
          [ngStyle]="{
            color: schedulerService.getScheduleColor(
              data.original.start,
              data.original.isDraftSchedule,
              data.original.instrumentFontColor,
              data.original.classType
            )
          }"
        >
          <div class="start-time mb-2">{{ data.original.start | date : constants.dateFormats.hh_mm_a }}</div>
          <div class="end-time">
            {{ data.original.end | date : constants.dateFormats.hh_mm_a }}
          </div>
        </div>
        <div class="info">
          <div
            class="instrument mb-2"
            [ngStyle]="{
              color: schedulerService.getScheduleColor(
                data.original.start,
                data.original.isDraftSchedule,
                data.original.instrumentFontColor,
                data.original.classType
              )
            }"
          >
            <span [ngClass]="{ strike: data.original.isCancelSchedule }">
              <span *ngIf="data.original.isCancelSchedule">Canceled: </span>
              @switch (data.original.classType) { @case (classTypes.GROUP_CLASS) {
              {{ data.original.groupClassName | titlecase }} ({{ getTimeDiff(data.original.start, data.original.end) }}) } @case
              (classTypes.SUMMER_CAMP) { {{ data.original.campName | titlecase }} ({{
                schedulerService.getNumberOfDays(data.original.campStartDate, data.original.campEndDate)
              }}d) } @case (classTypes.MAKE_UP) { {{ data.original.instrumentName }} Make-Up Lesson ({{
                getTimeDiff(data.original.start, data.original.end)
              }}) } @case (classTypes.ENSEMBLE_CLASS) { {{ data.original.ensembleClassName }} ({{
                getTimeDiff(data.original.start, data.original.end)
              }}) } @case (classTypes.INTRODUCTORY) { Introductory {{ data.original.instrumentName }} Lesson ({{
                getTimeDiff(data.original.start, data.original.end)
              }}) } @default { {{ data.original.instrumentName }} Lesson ({{ getTimeDiff(data.original.start, data.original.end) }}) } }
            </span>
          </div>
          <div class="lesson-details">
            <div class="lesson-item-wrapper">
              <img class="instructor-img" [attr.alt]="data.title" [src]="constants.staticImages.images.profileImgPlaceholder" />
              <div
                class="lesson-info-text"
                [ngStyle]="{
                  color: schedulerService.isPastDate(data.original.start) ? constants.disabledEventColors.color : 'lesson-info-text'
                }"
              >
                @if (data.original.classType === classTypes.ENSEMBLE_CLASS) {
                @for (assignedInstructors of data.original.assignedInstructors; track $index) {
                <ng-container *ngIf="$index < 1">
                   {{ assignedInstructors?.instructorName }}
                  <div class="dot " *ngIf="!$last"></div>
                </ng-container>
                } @if (data.original.assignedInstructors?.length>1) {
                  <div class="dot "></div>
                <div class="remaining-instrument-available-count" [matTooltip]="getInstructorNames(data.original.assignedInstructors)">
                  {{ data.original.assignedInstructors!.length - 1}}+
                </div>
                } } @else{
                {{ data.original.instructorName }}
                }
              </div>
            </div>

            <div class="dot hide-sm-screen-devices"></div>

            <div class="lesson-item-wrapper hide-sm-screen-devices">
              <img
                class="lesson-icons"
                [ngStyle]="{
                  filter: schedulerService.isPastDate(data.original.start) ? constants.filters.grayFilter : 'lesson-icons'
                }"
                [src]="constants.staticImages.icons.timeCircleClock"
                alt=""
              />
              <div
                class="lesson-info-text"
                [ngStyle]="{
                  color: schedulerService.isPastDate(data.original.start) ? constants.disabledEventColors.color : 'lesson-info-text'
                }"
              >
                {{ data.original.start | date : 'shortTime' }} - {{ data.original.end | date : 'shortTime' }} -
                {{ data.original.start | date : constants.fullDate }}
              </div>
            </div>

            @if (data.original.locationName) {
            <ng-container
              [ngTemplateOutlet]="lessonInfo"
              [ngTemplateOutletContext]="{
                text: data.original.locationName,
                icon: constants.staticImages.icons.location,
                start: data.original.start
              }"
            ></ng-container>
            } @if (data.original.roomName) {
            <ng-container
              [ngTemplateOutlet]="lessonInfo"
              [ngTemplateOutletContext]="{
                text: data.original.roomName,
                icon: constants.staticImages.icons.home,
                start: data.original.start
              }"></ng-container>
            } 
            @if (data.original.studentDetails?.length) {
            <ng-container
              [ngTemplateOutlet]="lessonInfo"
              [ngTemplateOutletContext]="{
                text: getStudentDisplayText(data),
                icon: constants.staticImages.icons.memberIcon,
                start: data.original.start
              }"></ng-container>
            }
          </div>
        </div>
        <div class="draft-badge" *ngIf="data.original.isDraftSchedule">
          <img [src]="constants.staticImages.icons.draftBadge" height="22" alt="" />
        </div>
      </div>
    </ng-template>
  </mbsc-eventcalendar>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>

<ng-template #lessonInfo let-text="text" let-icon="icon" let-start="start">
  <div class="dot"></div>

  <div class="lesson-item-wrapper">
    <img
      class="lesson-icons"
      [ngStyle]="{
        filter: schedulerService.isPastDate(start) ? constants.filters.grayFilter : 'lesson-icons'
      }"
      [src]="icon"
      alt=""/>
    <div
      class="lesson-info-text"
      [ngStyle]="{
        color: schedulerService.isPastDate(start) ? constants.disabledEventColors.color : 'lesson-info-text'
      }">
      {{ text | titlecase }}
    </div>
  </div>
</ng-template>
