import { FormControl } from '@angular/forms';
import { MbscCalendarEvent } from '@mobiscroll/angular';
import { RoomInstrumentList } from 'src/app/pages/room-and-location-management/models';
import { InstrumentsDetail } from 'src/app/request-information/models';
import { AdvanceFiltersParams } from '../../schedule/models';

export interface RoomSchedule {
  locationId: number;
  locationName: string;
  resource: number;
  roomName: string;
  scheduleStartDate: string;
  scheduleEndDate: string;
  instrumentId: number;
  instrumentName: string;
  subInstrumentId: number;
  subInstrumentName: string;
  name: string;
  start: string;
  end: string;
  isClosed: false;
  reason: string;
  roomInstruments: RoomInstrumentList[];
  instructorDetails: RoomInstructorDetails;
  scheduledInstruments: InstrumentsDetail[];
  scheduleDays: number[];
  id: number;
}

export interface RoomInstructorDetails {
  name: string;
  bio: string;
  profilePhoto: string;
  firstName: string;
  lastName: string;
  students: number;
  locationId: number;
  locationName: string;
  instruments: Array<InstrumentsDetail>;
  id: number;
  email: string;
  phoneNumber: string;
}

export interface AddRoomScheduleFormGroup {
  id: FormControl<number | undefined>;
  locationId: FormControl<number | undefined>;
  instructorId: FormControl<number | undefined>;
  roomId: FormControl<number | undefined>;
  scheduleDays: FormControl<number[]>;
  instumentIds: FormControl<number[]>;
  isAllInstances: FormControl<boolean>;
  start: FormControl<string>;
  end: FormControl<string>;
  scheduleStartDate: FormControl<string>;
  scheduleEndDate: FormControl<string>;
}

export interface ScheduleDataForDayView {
  locationId: number;
  locationName: string | undefined;
  schedulerData: Array<MbscCalendarEvent>;
}

export interface RoomParams {
  instrumentId: AdvanceFiltersParams;
}
