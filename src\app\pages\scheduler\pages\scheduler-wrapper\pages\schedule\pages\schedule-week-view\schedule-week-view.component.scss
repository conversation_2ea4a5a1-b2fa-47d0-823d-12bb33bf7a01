@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

::ng-deep {
  .week-view-wrapper {
    height: calc(100vh - 317px);
    border: 1px solid $btn-options-border-color;

    &.no-header {
      height: calc(100vh - 275px);
    }

    .mbsc-ios.mbsc-calendar-controls {
      display: none;
    }

    .mbsc-ios.mbsc-schedule-header-item-large.mbsc-schedule-header-item {
      padding-top: 10px;
      padding-bottom: 10px;
      border-right: 1px solid $btn-options-border-color;
    }

    .mbsc-ios.mbsc-schedule-header-item-large.mbsc-schedule-header-item:last-child {
      border-right: 0px;
    }

    .mbsc-ios.mbsc-schedule-header-item-large.mbsc-schedule-header-item:last-child {
      border-left: 1px solid $btn-options-border-color;
    }

    .mbsc-schedule-header-dayname,
    .mbsc-ios.mbsc-schedule-header-day {
      font-size: 15px;
      font-weight: 600;
      color: $gray-text;
    }

    .mbsc-schedule-time-col.mbsc-ltr {
      border-right: 1px solid $btn-options-border-color;
      font-weight: 700;
    }

    .mbsc-ios.mbsc-schedule-header-day.mbsc-selected,
    .mbsc-schedule-header-dayname.mbsc-selected {
      color: $primary-color;
      background: transparent;
      font-weight: 700;
    }

    .mbsc-ios.mbsc-popup-round .mbsc-popup {
      display: none;
    }

    .mbsc-ios.mbsc-schedule-time-indicator,
    .mbsc-ios.mbsc-schedule-time-indicator-time {
      border-color: $primary-color;
      color: $primary-color;
    }
  }

  .remaining-instrument-available-count {
    cursor: pointer;
    color: $black-color;
    font-weight: 800;
    font-size: 14px;
  }

  .events-detail-popup {
    .mbsc-popup-content {
      min-height: 400px;
    }
  }
}

.schedule-item {
  display: flex;
  border-radius: 5px;
  height: 100%;
  overflow: hidden;
  margin-right: auto;

  .schedule-border {
    margin-right: 5px;
    width: 10px;
    min-width: 10px;
    height: inherit;
  }

  .draft-badge {
    align-self: flex-start;
    padding-top: 5px;
  }

  .schedule-info-wrapper {
    color: $black-color;
    margin: 5px auto 5px 5px;

    .lesson-name {
      font-size: 14px;
      font-weight: 700;

      .camp-name {
        font-size: 12px;
        font-weight: 400;
      }
    }
    .instructor-info-wrapper {
      display: flex;

      img {
        margin-right: 5px;
        height: 18px;
      }

      .student-img {
        filter: $gray-filter;
      }

      .instructor-name {
        font-size: 12px;
      }
    }
  }
}

.schedule-popup-events {
  margin: 8px;
  cursor: pointer;

  .schedule-border {
    height: auto;
  }
}
