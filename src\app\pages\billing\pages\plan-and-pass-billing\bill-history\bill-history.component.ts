import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { DirectivesModule } from 'src/app/shared/directives/directives.module';
import { CBResponse } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { NgxPaginationModule } from 'ngx-pagination';
import { CommonUtils } from 'src/app/shared/utils';
import { ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { PlanAndPassBillingFilters, BillStatus, BillHistoryRes } from '../../../models';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { All } from 'src/app/pages/settings/pages/plan/models';
import { AuthService } from 'src/app/auth/services';
import { ActivatedRoute, Router } from '@angular/router';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import moment from 'moment';
import { MatIconModule } from '@angular/material/icon';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    DirectivesModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    NgxPaginationModule,
    SharedModule,
    MatSelectModule,
    FormsModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatIconModule
  ]
};

@Component({
  selector: 'app-bill-history',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  providers: [provideNativeDateAdapter()],
  templateUrl: './bill-history.component.html',
  styleUrl: './bill-history.component.scss'
})
export class BillHistoryComponent extends BaseComponent implements OnInit {
  billHistoryDetails!: Array<BillHistoryRes>;
  totalCount!: number;
  selectedDependentId!: number;
  classType = ClassTypes;
  billStatus = BillStatus;
  all = All;

  filters: PlanAndPassBillingFilters = {
    statusFilter: 0,
    startDateFilter: moment().subtract(1, 'month').format(this.constants.dateFormats.yyyy_MM_DD),
    endDateFilter: moment().format(this.constants.dateFormats.yyyy_MM_DD)
  };

  constructor(
    private readonly cdr: ChangeDetectorRef,
    protected readonly planSummaryService: PlanSummaryService,
    protected readonly schedulerService: SchedulerService,
    private readonly paymentService: PaymentService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly authService: AuthService,
    private readonly router: Router
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.getCurrentId();
          this.cdr.detectChanges();
        }
      });
  }

  getCurrentId(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params.dependentId) {
        this.selectedDependentId = +params.dependentId;
      } else {
        this.selectedDependentId = 0;
      }
      this.getBillHistoryDetails();
    });
  }

  openStudentDetailPage(dependentId: number): void {
    if (this.currentUser?.userRoleId === this.constants.roleIds.CLIENT) {
      return;
    }
    this.router.navigate([this.path.members.root, this.path.members.clients], {
      queryParams: { dependentId: dependentId }
    });
  }

  getFilterParams() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      UserId: this.currentUser?.userRoleId === this.constants.roleIds.CLIENT ? this.currentUser?.userId : undefined,
      dependentInformationId: this.selectedDependentId,
      CreatedStartDate: moment(this.filters.startDateFilter).format(this.constants.dateFormats.yyyy_MM_DD),
      CreatedEndDate: moment(this.filters.endDateFilter).format(this.constants.dateFormats.yyyy_MM_DD)
    });
  }

  getBillHistoryDetails(): void {
    if (!this.filters.endDateFilter) {
      return;
    }
    this.showPageLoader = true;
    this.paymentService
      .getListWithFilters<CBResponse<BillHistoryRes>>(this.getFilterParams(), `${API_URL.payment.getAllTransactionsOfUser}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<BillHistoryRes>) => {
          this.billHistoryDetails = res.result.items;
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }
}
