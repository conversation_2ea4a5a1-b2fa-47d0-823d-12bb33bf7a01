import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { takeUntil } from 'rxjs';
import { CommonModule, DatePipe } from '@angular/common';
import { provideNgxMask } from 'ngx-mask';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { CBResponse } from 'src/app/shared/models';
import { provideNativeDateAdapter } from '@angular/material/core';
import { SignUpTypeComponent } from '../../../../../../auth/pages/sign-up/sign-up-type/sign-up-type.component';
import { DependentService } from 'src/app/pages/profile/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import moment from 'moment';
import { ProfileBeforeSignUpParams, SignUpForOptions } from 'src/app/auth/models';
import { Account } from 'src/app/auth/models/user.model';

const DEPENDENCIES = {
  MODULES: [MatButtonModule, SharedModule, CommonModule],
  COMPONENTS: [SignUpTypeComponent]
};

@Component({
  selector: 'app-add-student',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter(), provideNgxMask()],
  templateUrl: './add-student.component.html',
  styleUrl: './add-student.component.scss'
})
export class AddStudentComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedAccManagerDetail!: Account | undefined;
  @Input() isAddDependent!: boolean;
  @Input() isEditAccountManager!: boolean;

  profileBeforeSignUpParams!: ProfileBeforeSignUpParams;
  locations!: Array<SchoolLocations>;

  @ViewChild(SignUpTypeComponent) signUpTypeComponent!: SignUpTypeComponent;
  @Output() closeSideNav = new EventEmitter<void>();
  @Output() refreshStudentList = new EventEmitter<void>();

  constructor(
    private readonly commonService: CommonService,
    private readonly cdr: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly datePipe: DatePipe,
    private readonly dependentService: DependentService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getAllLocations();
    if (this.isAddDependent || this.isEditAccountManager) {
      this.setProfileBeforeSignUpParams();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedAccManagerDetail']?.currentValue) {
      this.selectedAccManagerDetail = changes['selectedAccManagerDetail'].currentValue;
      this.setProfileBeforeSignUpParams();
    }
  }

  setProfileBeforeSignUpParams(): void {
    this.profileBeforeSignUpParams = {
      firstName: this.selectedAccManagerDetail?.firstName ?? '',
      lastName: this.selectedAccManagerDetail?.lastName ?? '',
      locationId: this.selectedAccManagerDetail?.locationId ?? undefined,
      dateOfBirth: this.selectedAccManagerDetail?.dateOfBirth ?? '',
      emailAddress: this.selectedAccManagerDetail?.emailAddress ?? '',
      phoneNumber: this.selectedAccManagerDetail?.phoneNumber ?? '',
      address: this.selectedAccManagerDetail?.address ?? '',
      stateId: this.selectedAccManagerDetail?.stateId ?? undefined,
      city: this.selectedAccManagerDetail?.city ?? '',
      zipCode: this.selectedAccManagerDetail?.zipCode ?? '',
      dependentId: this.selectedAccManagerDetail?.userType ?? undefined,
      userId: this.selectedAccManagerDetail?.userId ?? undefined,
      dependentDetails: (this.selectedAccManagerDetail?.dependentDetails as any) ?? [],
      allAttendedAtSameLocation:
        !!this.selectedAccManagerDetail?.dependentDetails?.length &&
        this.selectedAccManagerDetail?.dependentDetails.every(
          item => item.locationId === this.selectedAccManagerDetail?.dependentDetails[0].locationId
        ),
      referenceFrom: '',
      referenceBy: ''
    };
  }

  getAllLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  setStudentForm(): void {
    if (this.signUpTypeComponent.signUpTypeFormGroup.value.allAttendedAtSameLocation) {
      this.signUpTypeComponent.signUpTypeFormGroup.patchValue({
        dependentDetails: this.signUpTypeComponent.signUpTypeFormGroup.value.dependentDetails?.map(dependent => {
          return {
            ...dependent,
            locationId: this.signUpTypeComponent.signUpTypeFormGroup.value.locationId,
            dateOfBirth: this.datePipe.transform(dependent.dateOfBirth, this.constants.dateFormats.yyyy_MM_dd) ?? ''
          };
        }),
        dateOfBirth:
          this.datePipe.transform(
            this.signUpTypeComponent.signUpTypeFormGroup.getRawValue().dateOfBirth,
            this.constants.dateFormats.yyyy_MM_dd
          ) ?? ''
      });
    }
  }

  isUserUnderAge(dob: string): boolean {
    const birthDate = moment(dob);
    const age = moment().diff(birthDate, 'years');
    return age < 18;
  }

  deleteChildForYourself(): void {
    if(this.signUpTypeComponent.signUpTypeFormGroup.getRawValue().dependentId === SignUpForOptions.YOURSELF && this.signUpTypeComponent.previousDependentDetails.length) {
      this.signUpTypeComponent.deleteChildIds = [...this.signUpTypeComponent.previousDependentDetails.map((item) => item.id)];
    }
  }

  onSubmit(): void {
    this.setStudentForm();
    this.deleteChildForYourself();
    if (this.isUserUnderAge(this.signUpTypeComponent.signUpTypeFormGroup.getRawValue().dateOfBirth)) {
      this.toasterService.error(this.constants.errorMessages.userUnderAge);
      return;
    }
    if (this.signUpTypeComponent.signUpTypeFormGroup.invalid) {
      this.signUpTypeComponent.signUpTypeFormGroup.markAllAsTouched();
      return;
    }
    this.signUpTypeComponent.signUpTypeFormGroup.markAsUntouched();
    this.showBtnLoader = true;
    this.dependentService
      .add(
        {
          ...this.signUpTypeComponent.signUpTypeFormGroup.getRawValue(),
          userId: (this.isAddDependent || this.isEditAccountManager) ? this.selectedAccManagerDetail?.userId : undefined
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.deleteStudents();
          this.closeSideNavFun();
          this.refreshStudentList.emit();
          if(this.isEditAccountManager || this.isAddDependent) {
            this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Student'));
          }
          else {
            this.toasterService.success(this.constants.successMessages.addedSuccessfully.replace('{item}', 'Student'));
          }
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  deleteStudents() {
    if(!this.signUpTypeComponent.deleteChildIds.length) return;
    this.signUpTypeComponent.deleteChildIds?.forEach(id => {
      this.dependentService
        .delete(id, API_URL.crud.delete)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.signUpTypeComponent.deleteChildIds = [];
            this.cdr.detectChanges();
          },
          error: () => {
            this.cdr.detectChanges();
          }
        });
    });
  }

  closeSideNavFun(): void {
    this.signUpTypeComponent.getDependentInfoFormArray.clear();
    this.closeSideNav.emit();
    this.signUpTypeComponent.signUpTypeFormGroup.reset();
  }
}
