import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { LeaveBalance } from 'src/app/pages/requests/pages/leave-request/models';
import { FilterItem, InstructorInstrument } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { Instructor } from 'src/app/schedule-introductory-lesson/models';
import { IdNameModel } from 'src/app/shared/models';

export interface Instructors {
  instructorDetail: InstructorDetails;
}

export interface InstructorDetails extends Instructor {
  id: number;
  firstName: string;
  instruments: Array<InstructorInstrument>;
  lastName: string;
  locationId: number;
  bio: string;
  profilePhoto: string;
  name: string;
  locationName: string;
  students: number;
  email: string;
  phoneNumber: string;
  googleMeetLink: string;
  dateOfBirth: string;
  address: string;
  city: string;
  stateId: number;
  zipCode: string;
  stateAbbreviation: string;
  classRoomCode: string;
  instructors: number;
  instructorAvailability: Array<InstructorAvaibilityInInstructorDetail>;
  leaveBalances: Array<LeaveBalance>;
}

export interface InstructorAvaibilityInInstructorDetail {
  id: number;
  availableStartTime: string;
  availableEndTime: string;
  availableStartDate: string;
  availableEndDate: string;
  availableDays: Array<number>;
  availabilityType: number;
  locationId: number;
  locationsId: number;
  roomId: number;
  instructorId: number;
  roomName: string;
  locationName: string;
  deskManagerId: number;
  roomScheduleSummaryId: number;
}

export interface InstructorsFormGroup {
  id: FormControl<number | undefined>;
  name: FormControl<string>;
  email: FormControl<string>;
  phoneNumber: FormControl<string>;
  address: FormControl<string>;
  stateId: FormControl<number | undefined>;
  city: FormControl<string>;
  zipCode: FormControl<string>;
  dateOfBirth: FormControl<string>;
  isSupervisor: FormControl<boolean>;
  roleId: FormControl<number | undefined>;
  instructorInstrument: FormArray<FormGroup<InstructorInstrumentFormGroup>>;
  instructorAvailability: FormArray<FormGroup<InstructorAvailabilityFormGroup>>;
  leaveBalances: FormArray<FormGroup<LeaveBalanceFormGroup>>;
}

export interface InstructorInstrumentFormGroup {
  id: FormControl<number | undefined>;
  instrumentId: FormControl<number | undefined>;
  gradeLevel: FormControl<number>;
  isIntroductoryClassAvailable: FormControl<boolean>;
  isHeadOfDepartment: FormControl<boolean>;
}

export interface InstructorAvailabilityFormGroup {
  id: FormControl<number | undefined>;
  availableStartTime: FormControl<string>;
  availableEndTime: FormControl<string>;
  availableStartDate: FormControl<string>;
  availableEndDate: FormControl<string>;
  availableDays: FormControl<number[]>;
  availabilityType: FormControl<number>;
  locationId: FormControl<number | undefined>;
  roomId: FormControl<number | undefined>;
  roomScheduleSummaryId: FormControl<number | undefined>;
  instructorId: FormControl<number | undefined>;
  neverEnd: FormControl<boolean>;
}

export interface LeaveBalanceFormGroup {
  leaveType: FormControl<number>;
  totalLeaveDays: FormControl<number>;
  usedLeaveDays: FormControl<number>;
  remainingLeaveDays: FormControl<number>;
  memberDayViseLeave: FormArray<FormGroup<MemberDayWiseLeaveFormGroup>>;
}

export interface MemberDayWiseLeaveFormGroup {
  day: FormControl<number>;
  usedLeaveDays: FormControl<number>;
  remainingLeaveDays: FormControl<number>;
  totalLeaveDays: FormControl<number>;
}

export interface GradeLevel {
  gradeLevel: IdNameModel;
}

export interface InstructorInformationParams {
  startDate: string;
  endDate: string;
}

export interface InstructorFilters {
  searchTerm: string | null;
  locationId: FilterItem;
  instrumentId: FilterItem;
  isSupervisor?: number;
}

export interface LocationDetailInInstructor {
  count: number;
  names: string;
}

export interface LabelValueKey {
  label: string;
  value: number;
  key: string;
}

export enum AvailabilityType {
  NO_REPEATS = 1,
  DAILY = 2,
  WEEKLY = 3
}
