<div class="header-tab-with-btn">
  <div class="tab-item-content">
    @for (pageTabOption of pageTabOptions | keyvalue: keepOriginalOrder; track $index) {
    <div [ngClass]="{ item: true, 'active-item': selectedTabOption === pageTabOption.value }"
      (click)="setActiveTabOption(pageTabOption.value)">
      {{ pageTabOption.value }}
    </div>
    }
  </div>
  <div class="tab-item-content" *appHasPermission="[constants.roles.USER]">
    <div (click)="setActiveTabOption(paymentMethodTab)" class="payment-mentod">
      <mat-icon>credit_card</mat-icon>
      {{ paymentMethodTab }}
    </div>
  </div>
</div>

@switch (selectedTabOption) {
  @case (pageTabOptions.OPEN_BILL) {
    <app-open-bill></app-open-bill>
  }
  @case (pageTabOptions.BILL_HISTORY) {
    <app-bill-history></app-bill-history>
  }
  @case (paymentMethodTab) {
    <div class="payment-methods-wrapper">
      <app-payment-methods screen="billing-screen"></app-payment-methods>
    </div>
  }
}