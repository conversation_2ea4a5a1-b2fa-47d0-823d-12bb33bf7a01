import { Component, OnInit } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { DirectivesModule } from 'src/app/shared/directives/directives.module';
import { ActivatedRoute, Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { OpenBillComponent } from './open-bill/open-bill.component';
import { BillHistoryComponent } from './bill-history/bill-history.component';
import { PaymentMethodsComponent } from 'src/app/shared/components/payment-methods/payment-methods.component';

const DEPENDENCIES = {
  MODULES: [CommonModule, DirectivesModule, MatIconModule],
  COMPONENTS: [PaymentMethodsComponent, OpenBillComponent, BillHistoryComponent]
};

@Component({
  selector: 'app-plan-and-pass-billing',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './plan-and-pass-billing.component.html',
  styleUrl: './plan-and-pass-billing.component.scss'
})
export class PlanAndPassBillingComponent extends BaseComponent implements OnInit {
  selectedTabOption!: string;
  pageTabOptions = { OPEN_BILL: 'Open Bill', BILL_HISTORY: 'Bill History' };
  paymentMethodTab = 'Payment Method';

  constructor(
    protected readonly planSummaryService: PlanSummaryService,
    protected readonly schedulerService: SchedulerService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
  ) {
    super();
  }

  ngOnInit(): void {
    this.setActiveTabFromQueryParams();
  }

  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (Object.keys(params).length) {
        this.selectedTabOption = params.activeTab;
        return;
      }
      this.setActiveTabOption(this.pageTabOptions.OPEN_BILL);
    });
  }

  setActiveTabOption(tabName: string): void {
    this.selectedTabOption = tabName;
    this.router.navigate([this.path.billing.root, this.path.billing.planAndPass], {
      queryParams: {
        activeTab: tabName
      }
    });
  }

  keepOriginalOrder = () => 0;
}
