import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ROUTER_PATHS } from 'src/app/shared/constants';

const routes: Routes = [
  {
    path: '',
    redirectTo: ROUTER_PATHS.billing.planAndPass,
    pathMatch: 'full'
  },
  {
    path: ROUTER_PATHS.billing.planAndPass,
    loadComponent: async () => (await import('./pages/plan-and-pass-billing/plan-and-pass-billing.component')).PlanAndPassBillingComponent
  },
  {
    path: ROUTER_PATHS.billing.product,
    loadComponent: async () => (await import('./pages/product-billing/product-billing.component')).ProductBillingComponent
  } 
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BillingRoutingModule {}
