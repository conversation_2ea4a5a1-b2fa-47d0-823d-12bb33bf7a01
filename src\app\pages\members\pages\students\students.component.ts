import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';
import { NgxPaginationModule } from 'ngx-pagination';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { Debounce } from 'src/app/shared/decorators';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CommonUtils } from 'src/app/shared/utils';
import { MatSelectModule } from '@angular/material/select';
import { Instrument, InstrumentsDetail } from 'src/app/request-information/models';
import { DependentService } from 'src/app/pages/profile/services';
import { DependentInformations, StudentFilters, Students } from './models/students.model';
import { DashIfEmptyPipe } from 'src/app/shared/pipe';
import { AddStudentComponent } from './pages/add-student/add-student.component';
import { CommonService } from 'src/app/shared/services';
import { InstructorList } from 'src/app/schedule-introductory-lesson/models';
import { All } from 'src/app/pages/settings/pages/plan/models';
import { ViewStudentComponent } from './pages/view-student/view-student.component';
import { AssignPlanAndProductComponent } from './pages/assign-plan-and-product/assign-plan-and-product.component';
import { StudentPlans } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { AuthService } from 'src/app/auth/services';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { SupervisorFilter } from '../supervisors/models';
import { MatTooltipModule } from '@angular/material/tooltip';
import { InstructorInfo } from 'src/app/pages/schedule-classes/pages/group-class/models';
import { ActivatedRoute } from '@angular/router';
import { MultiSelectComponent } from 'src/app/shared/components/multi-select/multi-select.component';
import { SignUpForOptions } from 'src/app/auth/models';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    MatIconModule,
    MatInputModule,
    MatSidenavModule,
    NgxPaginationModule,
    CommonModule,
    SharedModule,
    MatSelectModule,
    MatTooltipModule
  ],
  COMPONENTS: [AddStudentComponent, ViewStudentComponent, AssignPlanAndProductComponent, MultiSelectComponent],
  PIPES: [DashIfEmptyPipe]
};

@Component({
  selector: 'app-students',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  templateUrl: './students.component.html',
  styleUrl: './students.component.scss'
})
export class StudentsComponent extends BaseComponent implements OnInit {
  totalCount!: number;
  students!: Array<Students>;
  instructors!: Array<InstructorList>;
  instruments!: Array<Instrument>;
  all = All;

  pageSize = this.paginationConfig.itemsPerPage;
  currentPage = this.paginationConfig.pageNumber;
  isAddStudentSideNavOpen = false;
  isAssignPlanSideNavOpen = false;
  isAddStudent = true;
  selectedStudentDetails!: DependentInformations | undefined;
  selectedStudentPlans!: Array<StudentPlans> | undefined;
  instructorIdUnderSupervisor!: Array<number>;
  accountManagerUserTypes = SignUpForOptions;

  filters: StudentFilters = {
    searchTerm: null,
    instructorId: {
      id: 1,
      defaultPlaceholder: 'All Instructors',
      placeholder: 'All Instructors',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    instrumentId: {
      id: 2,
      defaultPlaceholder: 'All Instruments',
      placeholder: 'All Instruments',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    age: 0,
    skillId: 0
  };

  @ViewChild(ViewStudentComponent) viewStudentComponent!: ViewStudentComponent;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly dependentService: DependentService,
    private readonly commonService: CommonService,
    private readonly authService: AuthService,
    private readonly instructorService: InstructorService,
    private readonly activatedRoute: ActivatedRoute
  ) {
    super();
  }

  ngOnInit(): void {
    this.getDependentIdFromQueryParams();
    this.getCurrentUser();
    this.getInstructors();
    this.getInstruments();
  }

  getDependentIdFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params.dependentId) {
        this.getDependentDetails(+params.dependentId, true);
      }
      this.cdr.detectChanges();
    });
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.getStudents(this.currentPage, this.pageSize);
  }

  @Debounce(300)
  getStudents(currentPage: number, pageSize: number): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();

    this.dependentService
      .add(this.getFilterParams(currentPage, pageSize), API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Students>) => {
          this.totalCount = res.result.totalCount;
          this.students = res.result.items;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParams(currentPage: number, pageSize: number) {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      filter: this.filters.searchTerm,
      instructorFilter: this.getInstructorFilter(),
      instrumentFilter: [...this.filters.instrumentId.value],
      page: currentPage,
      pageSize: pageSize
    });
  }

  getInstructorFilter(): Array<number> {
    switch (this.currentUser?.userRoleId) {
      case this.constants.roleIds.INSTRUCTOR:
        return [this.currentUser?.dependentId];
      case this.constants.roleIds.SUPERVISOR:
        return this.filters.instructorId
          ? Array.from(this.filters.instructorId.value)
          : [...this.instructorIdUnderSupervisor, this.currentUser?.dependentId];
      default:
        return this.filters.instructorId ? Array.from(this.filters.instructorId.value) : [];
    }
  }

  getDependentDetails(studentId: number, openViewPage: boolean): void {
    this.dependentService
      .getList<CBGetResponse<Students>>(`${API_URL.dependentInformations.getDependentInformationForView}?id=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<Students>) => {
          this.selectedStudentDetails = res.result.dependentInformation;
          openViewPage ? this.openAddOrViewStudent(false, this.selectedStudentDetails) : this.viewStudentComponent.setShowAccountManagerDetails(false);
          this.cdr.detectChanges();
        }
      });
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser$()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getAccountManagerDependentsName(accManager: DependentInformations): string {
    if (!accManager.accountManagerDependentsInfo.length) {
      return '';
    }
    return accManager.accountManagerDependentsInfo.map(dependent => `${dependent.firstName} ${dependent.lastName}`).join(', ');
  }

  getClientInfo(student: DependentInformations): string {
    switch (student.accountManagerUserType) {
      case this.accountManagerUserTypes.YOURSELF:
        return 'Managed by Own';
      case this.accountManagerUserTypes.YOUR_CHILD:
        return student.isAccountManager ? `Manages ${this.getAccountManagerDependentsName(student)}` : `Managed by ${student.accountManagerName}`;
      case this.accountManagerUserTypes.YOURSELF_AND_CHILD:
        return student.isAccountManager ? `Manages Own & ${this.getAccountManagerDependentsName(student)}` : `Managed by ${student.accountManagerName}`;
      default:
        return '';
    }
  }

  getInstructorNames(array: InstructorInfo[]): string {
    return array
      .slice(1)
      .map(item => item.name)
      .join(', ');
  }

  getInstrumentNames(array: InstrumentsDetail[]): string {
    return array
      .slice(1)
      .map(item => item.instrumentName)
      .join(', ');
  }

  openAddOrViewStudent(isAdd: boolean, dependentInfo?: DependentInformations): void {
    this.isAddStudent = isAdd;
    this.isAddStudentSideNavOpen = true;
    if (!isAdd) {
      this.selectedStudentDetails = dependentInfo;
    }
  }

  toggleAssignPlanAndProduct(isOpen: boolean, studentPlans?: Array<StudentPlans>): void {
    this.isAssignPlanSideNavOpen = isOpen;
    this.isAddStudentSideNavOpen = !isOpen;
    this.selectedStudentPlans = studentPlans;
    if (!isOpen) {
      this.getStudents(this.currentPage, this.pageSize);
    }
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.filters.instrumentId.options = this.instruments.map(instrument => ({
            id: instrument.instrumentDetail.id,
            name: instrument.instrumentDetail.name
          }));
          this.filters.instrumentId.totalCount = this.instruments.length;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParamsForInstructors() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      SupervisorIdFilter: this.currentUser?.isSupervisor ? this.currentUser?.dependentId : null,
      isSupervisorFilter: SupervisorFilter.ALL,
      Page: 1
    });
  }

  getInstructors(): void {
    this.showPageLoader = true;
    this.instructorService
      .add(this.getFilterParamsForInstructors(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<InstructorList>) => {
          this.instructors = res.result.items;
          this.instructorIdUnderSupervisor = this.instructors.map(instructor => instructor.instructorDetail.id);
          this.filters.instructorId.options = this.instructors.map(instructor => ({
            id: instructor.instructorDetail.id,
            name: instructor.instructorDetail.name
          }));
          this.filters.instructorId.totalCount = this.instructors.length;
          this.getStudents(this.currentPage, this.pageSize);
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInitials(firstName: string, lastName: string): string {
    return CommonUtils.getInitials(firstName, lastName);
  }

  @Debounce(300)
  onSearchTermChanged(): void {
    this.currentPage = 1;
    this.getStudents(this.currentPage, this.pageSize);
  }
}
