@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

.book-schedule-btn {
  @include flex-content-align-center;

  img {
    height: 16px;
    margin-right: 5px;
    filter: $white-filter;
  }

  .mobile-view {
    display: none;
  }
}

.auth-page-with-header {
  height: calc(100vh - 104px) !important;

  &.no-header {
    height: calc(100vh - 54px) !important;
  }
}

::ng-deep {
  .mat-drawer-content {
    position: unset !important;
  }
  .sidenav-content-without-footer {
    overflow: hidden !important;
  }
}

@media (max-width: 530px) {
  .book-schedule-btn {
    .mobile-view {
      display: block;
    }

    .lg-view {
      display: none;
    }
  }
}
