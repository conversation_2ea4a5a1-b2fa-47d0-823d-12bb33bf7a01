<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isAddStudentSideNavOpen || isAssignPlanSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-850"
    [disableClose]="true">
    @if (isAddStudentSideNavOpen) {
      <ng-container [ngTemplateOutlet]="isAddStudent ? addStudent : viewStudent"></ng-container>
    }
    @if (isAssignPlanSideNavOpen) {
      <app-assign-plan-and-product
        [selectedStudentDetails]="selectedStudentDetails"
        (closeSideNav)="toggleAssignPlanAndProduct(false)"></app-assign-plan-and-product>
    }
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="header-tab-only-btn">
      <button
        *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
        mat-raised-button
        color="primary"
        class="mat-primary-btn action-btn"
        type="button"
        (click)="openAddOrViewStudent(true)">
        Add Client
      </button>
    </div>

    <div class="auth-page-wrapper auth-page-with-header">
      <div class="filter-and-count-wrapper">
        <div class="search-and-count-wrapper-auth">
          <div class="total-users">
            Total: <span>{{ totalCount }}</span>
          </div>
          <div class="search-bar">
            <mat-form-field class="search-bar-wrapper ms-3">
              <input
                matInput
                placeholder="Search.."
                [(ngModel)]="filters.searchTerm"
                (ngModelChange)="onSearchTermChanged()" />
              <mat-icon matTextPrefix>search</mat-icon>
            </mat-form-field>
          </div>
        </div>
        <div class="filter-wrapper">
          <div
            class="search-bar-wrapper"
            *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER, constants.roles.SUPERVISOR]">
            <app-multi-select
              [filterDetail]="filters.instructorId"
              (selectedFilterValues)="getStudents((currentPage = 1), pageSize)">
            </app-multi-select>
          </div>

          <div class="search-bar-wrapper">
            <app-multi-select
              [filterDetail]="filters.instrumentId"
              (selectedFilterValues)="getStudents((currentPage = 1), pageSize)">
            </app-multi-select>
          </div>
        </div>
      </div>

      <div class="o-card">
        <div class="o-card-body">
          <div class="o-table">
            <div class="o-row o-header">
              <div class="o-cell first-cell">Client</div>
              <div class="o-cell" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
                Instructor
              </div>
              <div class="o-cell" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
                Instrument
              </div>
              <div class="o-cell" *appHasPermission="[constants.roles.SUPERVISOR, constants.roles.INSTRUCTOR]">Age</div>
              <div class="o-cell" *appHasPermission="[constants.roles.SUPERVISOR, constants.roles.INSTRUCTOR]">
                Account Manager Name
              </div>
              <div class="o-cell" *appHasPermission="[constants.roles.SUPERVISOR, constants.roles.INSTRUCTOR]">
                Account Manager Email
              </div>
            </div>
            <div class="dotted-divider"></div>
            <div class="content" [ngClass]="totalCount > 10 ? 'show-pagination' : 'hide-pagination'">
              <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : studentTemplate"></ng-container>
            </div>
          </div>
        </div>
      </div>
      @if (totalCount > 10) {
        <pagination-controls
          id="student"
          [previousLabel]="''"
          [nextLabel]="''"
          (pageChange)="onPageChange($event)"
          [responsive]="true"
          class="pagination-controls"></pagination-controls>
      }
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #addStudent>
  <app-add-student
    (closeSideNav)="isAddStudentSideNavOpen = false"
    (refreshStudentList)="getStudents(currentPage, pageSize)"></app-add-student>
</ng-template>

<ng-template #viewStudent>
  <app-view-student
    [selectedStudentDetails]="selectedStudentDetails"
    (closeViewSideNav)="isAddStudentSideNavOpen = false"
    (openDependentDetails)="getDependentDetails($event, false)"
    (refreshStudentList)="getStudents(currentPage, pageSize)"
    (toggleAssignPlanAndProduct)="toggleAssignPlanAndProduct(true, $event)"></app-view-student>
</ng-template>

<ng-template #studentTemplate>
  <ng-container [ngTemplateOutlet]="totalCount ? studentTableContent : noDataFound"></ng-container>
</ng-template>

<ng-template #studentTableContent>
  @for (
    student of students
      | paginate: { itemsPerPage: pageSize, currentPage: currentPage, totalItems: totalCount, id: "student" };
    track $index
  ) {
    <div class="o-row">
      <div
        class="o-cell first-cell instructor-name-photo-wrapper"
        (click)="openAddOrViewStudent(false, student.dependentInformation)">
        <div class="placeholder-name">
          <div>
            {{ getInitials(student.dependentInformation.firstName, student.dependentInformation.lastName) | uppercase }}
          </div>
        </div>
        <div class="student-name-wrapper">
          <div class="student-name text-truncate" [matTooltip]="student.dependentInformation.firstName + ' ' + student.dependentInformation.lastName">
            {{ student.dependentInformation.firstName | titlecase }}
            {{ student.dependentInformation.lastName | titlecase }}
          </div>
          <div class="student-manager text-truncate" [matTooltip]="getClientInfo(student.dependentInformation)">
            {{ getClientInfo(student.dependentInformation) | titlecase }}
          </div>
        </div>
      </div>
      <div class="o-cell text-gray" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        @if (student.dependentInformation.instructorsDetails.length) {
          <div class="instrument-wrapper">
            <div class="instrument-item">
              {{ student.dependentInformation.instructorsDetails[0].name | titlecase }}
            </div>
            @if (student.dependentInformation.instructorsDetails.length > 1) {
              <div class="dot"></div>
              <div
                class="remaining-instrument-available-count"
                [matTooltip]="getInstructorNames(student.dependentInformation.instructorsDetails)">
                {{ student.dependentInformation.instructorsDetails.length - 1 }}+
              </div>
            }
          </div>
        } @else {
          -
        }
      </div>
      <div class="o-cell text-gray" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        @if (student.dependentInformation.instruments.length) {
          <div class="instrument-wrapper">
            <div class="instrument-item">
              {{ student.dependentInformation.instruments[0].instrumentName }}
            </div>
            @if (student.dependentInformation.instruments.length > 1) {
              <div class="dot"></div>
              <div
                class="remaining-instrument-available-count"
                [matTooltip]="getInstrumentNames(student.dependentInformation.instruments)">
                {{ student.dependentInformation.instruments.length - 1 | dashIfEmpty }}+
              </div>
            }
          </div>
        } @else {
          -
        }
      </div>
      <div class="o-cell text-gray" *appHasPermission="[constants.roles.INSTRUCTOR, constants.roles.SUPERVISOR]">
        {{ student.dependentInformation.age }} Years
      </div>
      <div class="o-cell text-gray text-truncate" [matTooltip]="student.dependentInformation.accountManagerName" *appHasPermission="[constants.roles.INSTRUCTOR, constants.roles.SUPERVISOR]">
        {{ student.dependentInformation.accountManagerName | titlecase }}
      </div>
      <div class="o-cell text-gray text-truncate" [matTooltip]="student.dependentInformation.accountManagerEmail" *appHasPermission="[constants.roles.INSTRUCTOR, constants.roles.SUPERVISOR]">
        {{ student.dependentInformation.accountManagerEmail }}
      </div>
    </div>

    @if ($index < students.length - 1) {
      <div class="dotted-divider"></div>
    }
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-wrapper">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
