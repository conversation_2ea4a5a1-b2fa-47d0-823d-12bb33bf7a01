import { ActivatedRouteSnapshot } from '@angular/router';
import { ECharts, EChartsOption } from 'echarts';
import moment from 'moment';
import { Constants } from '../constants';

export class CommonUtils {
  static getFullName(firstName: string | undefined, lastName: string | undefined): string {
    return `${firstName || ''} ${lastName || ''}`;
  }

  static getInitials(firstName: string | undefined, lastName: string | undefined): string {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`;
  }

  static getInitialsUsingFullName(name: string | undefined): string {
    if (name?.includes(' ')) {
      return `${name?.split(' ')[0].charAt(0) || ''}${name?.split(' ')[1].charAt(0) || ''}`;
    } else {
      return `${name?.charAt(0)}`;
    }
  }

  static getPageTitle(routeSnapshot: ActivatedRouteSnapshot) {
    let title: string = routeSnapshot.data && routeSnapshot.data['pageTitle'] ? routeSnapshot.data['pageTitle'] : 'OMS';
    if (routeSnapshot.firstChild) {
      title = this.getPageTitle(routeSnapshot.firstChild) || title;
    }
    return title;
  }

  static formatNames(str: string, limit: number = 3): string {
    if (str.includes(',')) {
      const names = str.split(',');
      const displayedNames = names.slice(0, limit).join(', ');
      const remainingCount = names.length - limit;

      return remainingCount > 0 ? `${displayedNames} +${remainingCount}` : displayedNames;
    }
    return str;
  }

  static cleanObjectByRemovingKeysWithoutValue(obj: any): any {
    if (obj && typeof obj === 'object') {
      Object.keys(obj).forEach(key => {
        if (!obj[key] || obj[key] === '') {
          delete obj[key];
        }
      });
    }
    return obj;
  }

  static calculateTimeDifference(startDate: Date, endDate: Date): number {
    const differenceInMs = endDate.getTime() - startDate.getTime();
    return Math.floor(differenceInMs / (1000 * 60));
  }

  static b64toBlob = (b64Data: any, contentType = '', fileName: string) => {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];
    const sliceSize = 512;

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, { type: contentType });
    this.downloadFromBlob(blob, blob.type, fileName);
  };

  static downloadFromBlob(response: Blob, type: string, fileName: string): void {
    const blob = new Blob([response], { type: type });
    const url = window.URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(url);
  }

  static getAgeFromDob(dob: string): number {
    return moment().diff(moment(dob, moment.ISO_8601), 'years');
  }

  static formatHoursToHM(decimalHours: number): string {
    if (!decimalHours || decimalHours === 0) return '0m';

    const duration = moment.duration(decimalHours, 'hours');
    const hours = Math.floor(duration.asHours());
    const minutes = Math.floor(duration.minutes());

    if (hours === 0) {
      return `${minutes}m`;
    } else if (minutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${minutes}m`;
    }
  }

  static getHoursInRangeQuarter(startTime: string, endTime: string): number {
    const start = moment(new Date(startTime));
    const end = moment(new Date(endTime));
    const diffInMinutes = end.diff(start, 'minutes');
    return Math.round((diffInMinutes / 60) * 4) / 4;
  }

  static getDaysOfWeekBetween(startDate: string, endDate: string): number[] {
    if (!startDate || !endDate) return [];

    const start = moment(startDate);
    const end = moment(endDate);

    if (start.isSame(end, 'day')) {
      return [start.day()]; 
    }

    const days = new Set<number>();
    const current = start.clone();

    while (current.isSameOrBefore(end, 'day')) {
      days.add(current.day());
      current.add(1, 'day');
    }
    return Array.from(days);
  }

  static getChartOptions(availableLeaves: number, usedLeaves: number, totalLeaves: number): EChartsOption {
    // If total hours is 0, show a full completed circle
    if (totalLeaves === 0) {
      return {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}'
        },
        animation: false,
        series: {
          type: 'pie',
          radius: ['70%', '90%'],
          avoidLabelOverlap: false,
          startAngle: 90,
          label: {
            show: true,
            position: 'center',
            formatter: `{b|0/0}`,
            rich: {
              b: {
                fontSize: 18,
                fontWeight: 'normal',
                lineHeight: 30,
                color: 'black',
                fontFamily: 'Nunito Sans'
              }
            }
          },
          data: [
            {
              value: 0,
              name: 'Total Leaves',
              itemStyle: {
                color: Constants.colors.primaryColor
              },
              emphasis: {
                itemStyle: {
                  color: Constants.colors.primaryColor
                }
              }
            }
          ]
        }
      };
    }

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}'
      },
      animation: false,
      series: {
        type: 'pie',
        radius: ['70%', '90%'],
        avoidLabelOverlap: false,
        startAngle: 90,
        label: {
          show: true,
          position: 'center',
          formatter: `{b|${availableLeaves}/${totalLeaves}}`,
          rich: {
            b: {
              fontSize: 18,
              fontWeight: 'normal',
              lineHeight: 30,
              color: 'black',
              fontFamily: 'Nunito Sans'
            }
          }
        },
        data: [
          {
            value: availableLeaves,
            name: 'Available Leaves',
            itemStyle: {
              color: Constants.colors.primaryColor
            },
            emphasis: {
              itemStyle: {
                color: Constants.colors.primaryColor
              }
            }
          },
          {
            value: usedLeaves,
            name: 'Used Leaves',
            itemStyle: {
              color: Constants.colors.headerBgColor
            },
            emphasis: {
              itemStyle: {
                color: Constants.colors.headerBgColor
              }
            }
          }
        ]
      }
    };
  }

  static getWorkingHoursChartOptions(completedHours: number, pendingHours: number, totalHours: number): EChartsOption {
    // If total hours is 0, show a full completed circle
    if (totalHours === 0) {
      return {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}'
        },
        animation: false,
        series: {
          type: 'pie',
          radius: ['70%', '90%'],
          avoidLabelOverlap: false,
          startAngle: 90,
          label: {
            show: true,
            position: 'center',
            formatter: `{b|0m/0m}`,
            rich: {
              b: {
                fontSize: 18,
                fontWeight: 'normal',
                lineHeight: 30,
                color: 'black',
                fontFamily: 'Nunito Sans'
              }
            }
          },
          data: [
            {
              value: 0,
              name: 'Completed Hours',
              itemStyle: {
                color: Constants.colors.primaryColor
              },
              emphasis: {
                itemStyle: {
                  color: Constants.colors.primaryColor
                }
              }
            }
          ]
        }
      };
    }

    // Normal case with non-zero total hours
    return {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}'
      },
      animation: false,
      series: {
        type: 'pie',
        radius: ['70%', '90%'],
        avoidLabelOverlap: false,
        startAngle: 90,
        label: {
          show: true,
          position: 'center',
          formatter: `{b|${this.formatHoursToHM(completedHours)}/${this.formatHoursToHM(totalHours)}}`,
          rich: {
            b: {
              fontSize: 18,
              fontWeight: 'normal',
              lineHeight: 30,
              color: 'black',
              fontFamily: 'Nunito Sans'
            }
          }
        },
        data: [
          {
            value: completedHours,
            name: 'Completed Hours',
            itemStyle: {
              color: Constants.colors.primaryColor
            },
            emphasis: {
              itemStyle: {
                color: Constants.colors.primaryColor
              }
            }
          },
          {
            value: pendingHours,
            name: 'Pending Hours',
            itemStyle: {
              color: Constants.colors.headerBgColor
            },
            emphasis: {
              itemStyle: {
                color: Constants.colors.headerBgColor
              }
            }
          }
        ]
      }
    };
  }
}
