<div class="auth-page-with-header">
  <div class="search-and-count-wrapper-auth">
    <div class="search-and-count-wrapper">
      <div class="total-users">
        Total: <span class="border-0">{{ totalCount }}</span>
      </div>
    </div>

    <mat-form-field>
      <mat-date-range-input [rangePicker]="picker">
        <input matStartDate placeholder="Start date" [(ngModel)]="filters.startDateFilter" (click)="picker.open()" />
        <input matEndDate placeholder="End date" [(ngModel)]="filters.endDateFilter"
          (dateChange)="getBillHistoryDetails()" (click)="picker.open()" />
      </mat-date-range-input>
      <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
      <mat-date-range-picker #picker></mat-date-range-picker>
    </mat-form-field>
  </div>

  <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : billingLists"></ng-container>
</div>

<ng-template #billingLists>
  <ng-container [ngTemplateOutlet]="billHistoryDetails.length ? openBillList : noDataFound"></ng-container>
</ng-template>

<ng-template #openBillList>
  <div class="visits-list">
    @for (billHistory of billHistoryDetails; track $index) {
    <div class="o-card mb-2">
      <div class="o-card-body"
        [ngClass]="{ 'pointer': currentUser?.userRoleId === constants.roleIds.ADMIN || currentUser?.userRoleId === constants.roleIds.DESK_MANAGER }"
        (click)="openStudentDetailPage(billHistory.userTransactionDetails.dependentInformationId)">
        <div>
          <div class="title" *appHasPermission="[constants.roles.USER]">
            <ng-container [ngTemplateOutlet]="scheduleName" [ngTemplateOutletContext]="{ billHistory: billHistory }"></ng-container>
          </div>
          <div class="title admin-title" matTooltip="Account Manager"
            *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
             {{ billHistory.userTransactionDetails.accountManagerName | titlecase }}
          </div>

          <div class="visit-content mb-2" *appHasPermission="[constants.roles.USER]">
            <div class="visit-info">
              <div class="me-1">Date:</div>
              <div>
                {{
                billHistory.userTransactionDetails.scheduleStartDate | date: 'mediumDate'
                }} -
                {{
                billHistory.userTransactionDetails.scheduleEndDate | date: 'mediumDate'
                }}
              </div>
            </div>
            <div class="dot"></div>
            <div class="visit-info">
              <div class="me-1">Time:</div>
              <div>
                {{
                billHistory.userTransactionDetails.scheduleStartTime | date: 'shortTime'
                }} -
                {{
                billHistory.userTransactionDetails.scheduleEndTime | date: 'shortTime'
                }}
              </div>
            </div>
            <div class="dot"></div>
            <div class="visit-info">
              <div class="me-1">Transaction Id:</div>
              <div class="text-black">#{{ billHistory.userTransactionDetails.transactionId }}</div>
            </div>
          </div>
          <div class="visit-content mb-2" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
            <div class="visit-info">
              <div class="me-1">Email</div>
              <div class="text-black">{{ billHistory.userTransactionDetails.accountManagerEmail }}</div>
            </div>
            <div class="dot"></div>
            <div class="visit-info">
              <div class="me-1">Phone No.</div>
              <div class="text-black">{{ billHistory.userTransactionDetails.accountManagerPhone }}</div>
            </div>
            <div class="dot"></div>
            <div class="visit-info">
              <div class="me-1">Transaction Id:</div>
              <div class="text-black">#{{ billHistory.userTransactionDetails.transactionId }}</div>
            </div>
          </div>

          <div class="visit-content">
            <div class="visit-info">
              <div class="me-1">Payment Date:</div>
              <div class="primary-color">
                {{ billHistory.userTransactionDetails.billPaymentDate | date: 'mediumDate' }}
              </div>
            </div>
            <div class="dot"></div>
            <div class="visit-info">
              <div class="me-1 text-black">Dependent</div>
              <div class="primary-color">
                {{ billHistory.userTransactionDetails.dependentName | titlecase }}
              </div>
            </div>
            <div class="dot"></div>
            <div class="visit-info" *appHasPermission="[constants.roles.USER]">
              <div class="primary-color me-1">
                {{
                schedulerService.getClassType(billHistory.userTransactionDetails.scheduleType)
                }}
              </div>
              <div>Lesson</div>
            </div>
            <div class="visit-info" [matTooltip]="schedulerService.getClassType(billHistory.userTransactionDetails.scheduleType) + ' Lesson'" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
              <ng-container [ngTemplateOutlet]="scheduleName" [ngTemplateOutletContext]="{ billHistory: billHistory }"></ng-container>
            </div>
          </div>
        </div>

        <div class="visit-cancel-info">
          @if (billHistory.userTransactionDetails.discountedAmount) {
          <div [matTooltip]="'Discounted Amount: $' + billHistory.userTransactionDetails.discountedAmount">
            {{
            billHistory.userTransactionDetails.paidAmount | currency: 'USD' : true : '1.2-2'
            }}
          </div>
          }
          @else {
          <div>
            {{
            billHistory.userTransactionDetails.paidAmount | currency: 'USD' : true : '1.2-2'
            }}
          </div>
          }
        </div>
      </div>
    </div>
    }
  </div>
</ng-template>

<ng-template #scheduleName let-billHistory="billHistory">
  @switch (billHistory.userTransactionDetails.scheduleType) {
  @case (classType.INTRODUCTORY) {
  <div>
    Introductory {{ billHistory.userTransactionDetails.planInstrument | titlecase }} Lesson
  </div>
  }
  @case (classType.RECURRING) {
  <div>
    Weekly {{ billHistory.userTransactionDetails.planInstrument | titlecase }} Lessons
    ({{
    planSummaryService.getPlanType(billHistory.userTransactionDetails.planType)
    }} -
    {{
    planSummaryService.getPlanSummary(billHistory.userTransactionDetails.planDetails)
    }})
  </div>
  }
  @case (classType.GROUP_CLASS) {
  <div>
    {{ billHistory.userTransactionDetails.groupClassName | titlecase }}
  </div>
  }
  @case (classType.SUMMER_CAMP) {
  <div>
    {{ billHistory.userTransactionDetails.summerCampName | titlecase }}
  </div>
  }
  @case (classType.MAKE_UP) {
  <div>
    {{ billHistory.userTransactionDetails.planInstrument }} Make-Up Lesson
  </div>
  }
  @case (classType.ENSEMBLE_CLASS) {
  <div>
    {{ billHistory.userTransactionDetails.ensembleClassName | titlecase }}
  </div>
  }
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>