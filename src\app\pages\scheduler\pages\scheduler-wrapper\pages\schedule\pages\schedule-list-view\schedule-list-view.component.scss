@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

.list-view-wrapper {
  border: 1px solid $btn-options-border-color;
  height: calc(100vh - 317px);

  &.no-header {
    height: calc(100vh - 275px);
  }

  .current-date-event-count {
    border-bottom: 1px solid $btn-options-border-color;
    padding: 15px;
    font-weight: 600;
    @include flex-content-align-center;

    .events {
      color: $gray-text;
    }
  }

  .agenda-wrapper {
    padding: 15px 0;
    min-height: 295px;
    height: calc(100vh - 360px);

    .schedule-item {
      width: 100%;
      height: 75px;
      margin: 0 15px;
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
      @include flex-content-align-center;

      .schedule-border {
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
        width: 10px;
        height: inherit;
      }

      .time-wrapper,
      .info {
        margin-left: 10px;
      }

      .draft-badge {
        align-self: flex-start;
        padding-top: 17px;
      }

      .time-wrapper {
        min-width: 72px;

        .start-time {
          font-weight: 700;
        }
      }

      .info {
        margin-right: auto;
        .instrument {
          font-weight: 700;
        }

        .lesson-details {
          @include flex-content-align-center;
          font-size: 14px;
          flex-wrap: wrap;

          .lesson-item-wrapper {
            @include flex-content-align-center;
            .instructor-img {
              height: 18px;
              margin-right: 5px;
            }

            .lesson-icons {
              height: 14px;
              width: 14px;
              margin-right: 5px;
              filter: $black-filter;
            }

            .lesson-info-text {
              line-height: 17px;
              display: flex;
              align-items: center;
            }
          }

          .dot {
            background: $dot-dark-gray-color;
          }
        }
      }
    }

    .remaining-instrument-available-count {
      cursor: pointer;
      color: $black-color;
      font-weight: 800;
      font-size: 14px;
    }

    ::ng-deep {
      .mbsc-windows.mbsc-list-item {
        padding: 0 !important;
        max-height: 75px;
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
      }

      .mbsc-windows.mbsc-event.mbsc-list-item {
        margin-bottom: 10px;
      }

      .mbsc-windows.mbsc-event.mbsc-list-item:last-child {
        margin-bottom: 0px;
      }

      .mbsc-windows.mbsc-calendar-header,
      .mbsc-windows.mbsc-schedule-date-header-text,
      .mbsc-ios.mbsc-eventcalendar .mbsc-calendar-wrapper,
      .mbsc-ios.mbsc-schedule-date-header,
      .mbsc-windows.mbsc-event-day.mbsc-list-header {
        display: none;
      }

      .mbsc-windows.mbsc-schedule-date-header,
      .mbsc-windows.mbsc-calendar-wrapper-fixed,
      .mbsc-windows.mbsc-event-group,
      .mbsc-ios.mbsc-event.mbsc-list-item:before,
      .mbsc-ios.mbsc-event.mbsc-list-item:after {
        border: none;
      }

      .mbsc-windows.mbsc-event.mbsc-focus,
      .mbsc-windows.mbsc-event.mbsc-selected,
      .mbsc-windows.mbsc-list-item.mbsc-hover:before {
        background: transparent !important;
      }

      .mbsc-ios.mbsc-list-item {
        padding: 7px 0;
      }
    }
  }
}

@media (max-width: 530px) {
  .list-view-wrapper {
    .current-date-event-count {
      flex-wrap: wrap;
    }

    .agenda-wrapper {
      padding: 15px 0px;

      .schedule-item {
        flex-wrap: wrap;
        height: auto;

        .info {
          .instrument {
            margin-top: 5px;
          }

          .lesson-details {
            flex-wrap: wrap;
          }

          .lesson-item-wrapper {
            width: 100%;
            margin-bottom: 5px;
          }

          .dot {
            display: none;
          }
        }
      }
    }

    ::ng-deep {
      .mbsc-calendar-wrapper,
      .mbsc-ios.mbsc-schedule-date-header-text {
        display: none;
      }

      .mbsc-ios.mbsc-event.mbsc-list-item {
        border: none !important;
      }

      .mbsc-ios.mbsc-list-item:before,
      .mbsc-ios.mbsc-list-item:after {
        border-top: none !important;
      }

      .mbsc-ios.mbsc-list-item {
        padding: 0.5em;
      }
    }
  }
}

::ng-deep {
  .mbsc-popup-content {
    min-height: 400px;
  }
}

@media (max-width: 1037px) {
  .hide-sm-screen-devices {
    display: none !important;
  }
}

// Leave event styles
::ng-deep .leave-event {
  .time-wrapper {
    .leave-type {
      font-size: 0.8em;
      font-weight: 500;
      margin-top: 2px;
    }
  }
}
