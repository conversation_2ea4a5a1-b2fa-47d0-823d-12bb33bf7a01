<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isNotificationPanelOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="md-sidebar"
    [disableClose]="true"
  >
    @if (isNotificationPanelOpen) {
      <app-notification-panel [unreadNotificationCount]="unreadNotificationCount" (refreshUnreadCount)="getUnreadNotificationCount()" (closeSideNav)="toggleNotificationPanel(false)"></app-notification-panel>
    }
  </mat-sidenav>
</mat-sidenav-container>

<div class="header-element-wrapper">
  <div class="dependent-info" *ngIf="showDependentInfo && currentUser?.dependentDetails?.length">
    <div class="btn-typed-options-wrapper">
      <div [ngClass]="{
        'btn-typed-option': true,
        active: selectedDependentId === 0
      }" (click)="onSelectDependent(0)">All</div>

      <div *ngIf="currentUser?.dependentId" [ngClass]="{
        'btn-typed-option': true,
        active: selectedDependentId === currentUser?.dependentId
      }" (click)="onSelectDependent(currentUser!.dependentId)">{{ currentUser?.firstName | titlecase }} {{ currentUser?.lastName | titlecase }}</div>
      
      @for (dependent of currentUser?.dependentDetails; track $index) {
        <div [ngClass]="{
          'btn-typed-option': true,
          active: selectedDependentId === dependent.id
        }" (click)="onSelectDependent(dependent.id)">
        {{ dependent.firstName | titlecase }} {{ dependent.lastName | titlecase }}
      </div> 
      }
    </div>
  </div>
  <div class="header-element-container" [ngClass]="{ 'w-100': !showDependentInfo || !currentUser?.dependentDetails?.length }">
    <div class="position-relative">
      <img
        [src]="constants.staticImages.icons.bellIcon"
        alt=""
        class="notification"
        (click)="toggleNotificationPanel(true)"
      />
      @if (unreadNotificationCount) {
      <div class="unread-badge">
          {{ unreadNotificationCount > 99 ? '99+' : unreadNotificationCount }}
        </div>
      }
    </div>
  
    @if (!showPageLoader) {
      @if (currentUser?.profilePicturefullurl) {
        <img
          [src]="currentUser?.profilePicturefullurl ?? ''"
          [appImageLoader]="constants.staticImages.loaders.imageLoader"
          [appImagePlaceholder]="constants.staticImages.images.profileImgPlaceholder"
          alt="Profile image"
          class="profile-img pointer"
          [routerLink]="path.profile.root"
          [queryParams]="{ activeTab: 'Account Profile' }"
        />
      } @else {
        <div
          class="placeholder-name"
          [routerLink]="path.profile.root"
          [queryParams]="{ activeTab: 'Account Profile' }"
        >
          {{ setInitials(currentUser?.firstName, currentUser?.lastName) | uppercase }}
        </div>
      }
    }
    <img
      [src]="constants.staticImages.icons.logOut"
      alt=""
      class="logout"
      (click)="openLogoutConfirmationDialog()"
    />
  </div>
</div>

<ng-template #logoutDialog>
  <h2 mat-dialog-title>Logout</h2>
  <mat-dialog-content>
    <p>Are you sure you want to logout?</p>
  </mat-dialog-content>
  <mat-dialog-actions [align]="'end'">
    <button mat-raised-button color="accent" class="mat-accent-btn back-btn" (click)="closeDialog()">
      Cancel
    </button>
    <button mat-raised-button color="warn" class="mat-warn-btn" (click)="confirmLogout()">
      Logout
    </button>
  </mat-dialog-actions>
</ng-template>
