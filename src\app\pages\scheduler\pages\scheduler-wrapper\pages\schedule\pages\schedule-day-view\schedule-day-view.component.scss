@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

::ng-deep {
  .day-view-wrap-loader {
    justify-content: center;
    border: 1px solid $btn-options-border-color;
    padding: 25px;
  }

  .day-view-wrap-one-item {
    display: block !important;
  }

  .day-view-wrap {
    display: flex;
    flex-wrap: wrap;
    height: calc(100vh - 317px);
    overflow: auto;

    &.no-header {
      height: calc(100vh - 275px);
    }

    .day-view-wrapper {
      min-width: 48%;
      max-width: 48%;
      margin-right: 15px;
      border: 1px solid $btn-options-border-color;

      .day-view-header {
        border-bottom: 0;
        background-color: #6c0345;
        color: $white-color;
        font-weight: 600;
        font-size: 16px;
        display: flex;

        .empty-box {
          width: 67px;
          background-color: $white-color;
          height: 40px;
          border-right: 1px solid #cccccc;
        }

        .location-name {
          margin: 8px;
          @include flex-content-space-between;
          width: calc(100% - 83px);

          mat-icon {
            color: $white-color !important;
            height: auto;
            font-size: 16px !important;
            cursor: pointer;
          }
        }
      }

      .day-view-content {
        .mbsc-ios.mbsc-schedule-date-header,
        .mbsc-ios.mbsc-calendar-wrapper-fixed {
          display: none;
        }

        .mbsc-ios.mbsc-schedule-resource.mbsc-ltr,
        .mbsc-schedule-column.mbsc-ltr {
          min-width: 200px;
        }

        .mbsc-ios.mbsc-schedule-time-indicator {
          border-color: $primary-color;
        }

        .mbsc-ios.mbsc-schedule-invalid {
          background: #d5d5d54a;
        }

        .mbsc-ios.mbsc-schedule-time-indicator-time {
          color: $primary-color;
        }

        .mbsc-schedule-time-col.mbsc-ltr {
          border-right: 1px solid #cccccc;
          background: $white-color;
        }

        .mbsc-timeline-grid-scroll,
        .mbsc-schedule-grid-scroll {
          height: calc(100vh - 397px);
        }
      }
    }
  }

  .mbsc-popup-content {
    min-height: 400px;
  }
}

.schedule-item {
  display: flex;
  border-radius: 5px;
  height: 100%;
  overflow: hidden;

  .schedule-border {
    margin-right: 5px;
    width: 10px;
  }

  .draft-badge {
    align-self: flex-start;
  }

  .schedule-info-wrapper {
    margin-right: auto;
    color: $black-color;

    .lesson-name {
      font-size: 14px;
      font-weight: 700;

      .camp-name {
        font-size: 12px;
        font-weight: 400;
      }
    }
    .instructor-info-wrapper {
      display: flex;

      img {
        margin-right: 5px;
      }

      .instructor-name {
        font-size: 12px;
      }
    }
  }
}

::ng-deep .content-loader {
  height: calc(100vh - 317px);
  margin-top: 0 !important;
}

.instructor-wrapper {
  font-size: 16px;
  color: $gray-text;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 170px;
}
