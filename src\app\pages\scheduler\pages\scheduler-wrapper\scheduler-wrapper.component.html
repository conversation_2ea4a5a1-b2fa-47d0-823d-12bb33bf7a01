<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
  [opened]="isAddScheduleOpen || isBulkCancelOpen"
  mode="over"
  position="end"
  fixedInViewport="true"
  [ngClass]="'sidebar-w-750'"
  [disableClose]="true">
  @if (isAddScheduleOpen) {
     @if (selectedTabOption === pageTabOptions.SCHEDULE) {
        <app-add-schedule
          (closeModal)="isAddScheduleOpen = false"
          (refreshScheduleData)="updateScheduleData()"></app-add-schedule>
      } @else {
        <app-add-room-schedule
          [selectedRoom]="selectedRoom"
          (closeModal)="toggleRoomSideNav(null, false)"
          (refreshScheduleData)="updateRoomData()"></app-add-room-schedule>
      }
  }
  @if (isBulkCancelOpen) {
    <app-bulk-cancel
      [startDate]="scheduleDate"
      (closeSideNav)="isBulkCancelOpen = false"
      (refreshScheduleData)="updateScheduleData()"></app-bulk-cancel>
  }
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="header-tab-with-btn" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
      <div class="tab-item-content">
        @for (pageTabOption of pageTabOptions | keyvalue: asIsOrder; track $index) {
          <div
            [ngClass]="{ item: true, 'active-item': selectedTabOption === pageTabOption.value }"
            (click)="setActiveTabOption(pageTabOption.value)">
            {{ pageTabOption.value }}
          </div>
        }
      </div>
      <div class="action-btn-wrapper">
        @if (selectedTabOption === pageTabOptions.SCHEDULE) {
          <button
          mat-raised-button
          color="primary"
          class="mat-red-btn action-btn"
          type="button"
          (click)="isBulkCancelOpen = true">
          <div class="book-schedule-btn">
            <img [src]="constants.staticImages.icons.crossCircle" alt="">
            <div>Bulk Cancel</div>
          </div>
         </button>
        }
        <button
          mat-raised-button
          color="primary"
          class="mat-primary-btn action-btn"
          type="button"
          (click)="isAddScheduleOpen = true">
          <div class="book-schedule-btn">
            @if (selectedTabOption === pageTabOptions.SCHEDULE) {
              <img [src]="constants.staticImages.icons.addNew" alt="" />
              <div>Book</div>
            } @else {
              <div class="mobile-view"><img [src]="constants.staticImages.icons.addNew" alt="" /></div>
              <div class="lg-view">Add Room Schedule</div>
            }
          </div>
        </button>
      </div>
    </div>

    <div class="auth-page-with-header" [ngClass]="{ 'no-header': currentUser?.userRoleId === constants.roleIds.INSTRUCTOR || currentUser?.userRoleId === constants.roleIds.SUPERVISOR }">
      @if (selectedTabOption === pageTabOptions.SCHEDULE) {
        <app-schedule (scheduleDate)="scheduleDate = $event"></app-schedule>
      } @else {
        <app-room-schedule (selectedRoom)="toggleRoomSideNav($event, true)"></app-room-schedule>
      }
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>
