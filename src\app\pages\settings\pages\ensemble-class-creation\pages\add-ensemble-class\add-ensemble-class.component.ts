import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CommonModule, DatePipe } from '@angular/common';
import { RoomDetails, SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import { Instrument } from 'src/app/request-information/models';
import { RoomService } from 'src/app/pages/room-and-location-management/pages/room/services';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { Instructor } from 'src/app/schedule-introductory-lesson/models';
import {
  ClassTypes,
  InstructorAvaibility,
  SuggestedTimeSlot
} from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { EnumToKeyValuePipe } from 'src/app/shared/pipe';
import { Duration } from '../../../plan/models/plan-summary.model';
import { RevenueCategory } from '../../../revenue-categories/models';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import moment from 'moment';
import { EnsembleClassFormGroup, EnsembleClassFormParams } from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { MultiSelectChipsComponent } from 'src/app/shared/components/multi-select-chips/multi-select-chips.component';
import { EnsembleClassesService } from 'src/app/pages/schedule-classes/pages/ensemble-class/services';
import { Debounce } from 'src/app/shared/decorators';
import { CommonUtils } from 'src/app/shared/utils';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    ReactiveFormsModule,
    MatInputModule,
    MatFormFieldModule,
    SharedModule,
    MatSelectModule,
    CommonModule,
    MatCheckboxModule,
    MatDatepickerModule,
    FormsModule,
    MultiSelectChipsComponent
  ],
  PIPES: [EnumToKeyValuePipe]
};

@Component({
  selector: 'app-add-ensemble-class',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  providers: [provideNativeDateAdapter()],
  templateUrl: './add-ensemble-class.component.html',
  styleUrl: './add-ensemble-class.component.scss'
})
export class AddEnsembleClassComponent extends BaseComponent implements OnInit {
  ensembleClassFormGroup!: FormGroup<EnsembleClassFormGroup>;
  locations!: Array<SchoolLocations>;
  instruments!: Array<Instrument>;
  rooms!: Array<RoomDetails>;
  instructors!: Array<Instructor> | undefined;
  revenueCategories!: Array<RevenueCategory>;
  maxDate = new Date();
  durations = Duration;
  classTypes = ClassTypes;
  suggestedTimeSlots!: SuggestedTimeSlot[] | undefined;
  selectedTimeSlot!: string | null;
  searchTerm!: string;

  filterParams: EnsembleClassFormParams = {
    instrument: {
      id: 1,
      defaultPlaceholder: 'Select Instuments',
      placeholder: 'Select Instuments',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      showMax: 4,
      isOpen: false,
      showSearchBar: true,
      options: [] as Array<IdNameModel>
    },
    instructor: {
      id: 1,
      defaultPlaceholder: 'Select Instructor',
      placeholder: 'Select Instructor',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      showMax: 3,
      isOpen: false,
      showSearchBar: true,
      options: [] as Array<IdNameModel>
    }
  };

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() isEnsembleClassAdded = new EventEmitter<void>();

  constructor(
    private readonly datePipe: DatePipe,
    private readonly toasterService: AppToasterService,
    private readonly roomService: RoomService,
    private readonly commonService: CommonService,
    private readonly dialog: MatDialog,
    private readonly ensembleClassService: EnsembleClassesService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initGroupClassForm();
    this.getLocations();
    this.getInstruments();
    this.getRevenueCategories();
  }

  initGroupClassForm(): void {
    this.ensembleClassFormGroup = new FormGroup<EnsembleClassFormGroup>({
      id: new FormControl(undefined, { nonNullable: true }),
      ensembleClassName: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      duration: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      skillType: new FormControl(undefined, { nonNullable: true }),
      ageGroup: new FormControl(undefined, { nonNullable: true }),
      lessonType: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      description: new FormControl('', { nonNullable: true }),
      assignedInstruments: new FormControl([], { nonNullable: true, validators: [Validators.required] }),
      assignedInstructors: new FormControl([], { nonNullable: true, validators: [Validators.required] }),
      locationId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      roomId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      studentCapacity: new FormControl(this.constants.defaultStudentCapacity, {
        nonNullable: true,
        validators: [Validators.required, Validators.min(1)]
      }),
      isWaitlistAvailable: new FormControl(false, { nonNullable: true, validators: [Validators.required] }),
      scheduleStartDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleEndDate: new FormControl('', { nonNullable: true }),
      enrollLastDate: new FormControl(undefined, { nonNullable: true }),
      daysOfSchedule: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleStartTime: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleEndTime: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      price: new FormControl(undefined, { nonNullable: true }),
      categoryId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] })
    });
  }

  getFilterParamsForRooms() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      page: 1,
      locationIdFilter: [this.ensembleClassFormGroup.controls.locationId.value]
    });
  }

  getRooms(): void {
    this.roomService
      .add(this.getFilterParamsForRooms(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RoomDetails>) => {
          this.rooms = res.result.items;
          this.ensembleClassFormGroup.controls.roomId.reset();
          this.cdr.detectChanges();
        }
      });
  }

  @Debounce(500)
  getInstructors(): void {
    const control = this.getInstructorAvailability;
    if (
      control.daysOfSchedule?.length &&
      control.locationId &&
      control.scheduleStartDate &&
      control.duration &&
      control.instrumentIds
    ) {

      this.ensembleClassService
        .add(this.getInstructorAvailability, API_URL.ensembleClassScheduleSummaries.instructorAvailabilities)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<Instructor[]>) => {
            this.instructors = response.result;
            this.filterParams.instructor.options = this.instructors.map(instructors => ({
              id: instructors.id,
              name: `${instructors.name}`
            }));
            this.ensembleClassFormGroup.controls.assignedInstructors.reset();
            this.cdr.detectChanges();
          }
        });
    }
  }

  getRoomsAndInstructors(): void {
    this.getRooms();
    this.getInstructors();
  }

  setEnrollDate(): void {
    const scheduleStartDate = moment(this.ensembleClassFormGroup.getRawValue().scheduleStartDate);
    const today = moment().startOf('day');
    const twoDaysBefore =
      scheduleStartDate.diff(today, 'days') > 1 ? scheduleStartDate.subtract(2, 'days').toDate() : scheduleStartDate.toDate();
    this.setFormControlValue('enrollLastDate', twoDaysBefore);
  }

  getSuggestedTimeAndInstructors(clearEndDate: boolean): void {
    if (clearEndDate) {
      this.ensembleClassFormGroup.controls.scheduleEndDate.reset();
    }
    this.getSuggestedTime();
    this.getInstructors();
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.filterParams.instrument.options = this.instruments.map(instrument => ({
            id: instrument.instrumentDetail.id,
            name: `${instrument.instrumentDetail.name}`
          }));
          this.cdr.detectChanges();
        }
      });
  }

  getRevenueCategories(): void {
    this.commonService
      .getRevenueCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RevenueCategory>) => {
          this.revenueCategories = res.result.items;
          const id = this.revenueCategories.find(
            item => item.category.categoryName === this.constants.revenueCategories.introductoryEnsembleClassRevenue
          )?.category.id!;
          this.setFormControlValue('categoryId', id);
          this.cdr.detectChanges();
        }
      });
  }

  setFormControlValue(controlName: string, value: string | number | boolean | Date | number[]): void {
    (this.ensembleClassFormGroup.controls as any)[controlName].setValue(value);
    if (controlName === 'daysOfSchedule' || controlName === 'duration' || controlName === 'skillType') {
      this.getInstructors();
    }
  }

  setStartAndEndTime(selectedTimeSlot: SuggestedTimeSlot): void {
    this.selectedTimeSlot = `${selectedTimeSlot.startTime} - ${selectedTimeSlot.endTime}`;
    this.ensembleClassFormGroup.patchValue({
      scheduleStartTime: selectedTimeSlot.startTime,
      scheduleEndTime: selectedTimeSlot.endTime
    });
  }

  setInsrtumentIdDetails(): void {
    const instrument = this.filterParams.instrument.value.map(instrument => instrument.id);
    this.setFormControlValue('assignedInstruments', instrument);
  }
  
  setInstructorIdDetails(): void {
    const instructor = this.filterParams.instructor.value.map(instructor => instructor.id);
    this.setFormControlValue('assignedInstructors', instructor);
  }

  resetInstructors(): void {
    this.filterParams.instructor.value = [];
    this.setFormControlValue('assignedInstructors', []);
  }

  get getInstructorAvailability(): InstructorAvaibility {
    return {
      scheduleStartDate: this.datePipe.transform(
        this.ensembleClassFormGroup.controls.scheduleStartDate.value,
        this.constants.dateFormats.yyyy_MM_dd
      ),
      scheduleEndDate: moment(this.ensembleClassFormGroup.controls.scheduleStartDate.value).add(1, 'year').format(this.constants.dateFormats.yyyy_MM_DD),
      locationId: this.ensembleClassFormGroup.controls.locationId.value,
      instrumentIds: this.filterParams.instrument.value.map(a => a.id),
      instructorIds: this.filterParams.instructor.value.map(a => a.id),
      daysOfSchedule: this.ensembleClassFormGroup.controls.daysOfSchedule.value
        ? [Number(this.ensembleClassFormGroup.controls.daysOfSchedule.value)]
        : [],
      duration: this.ensembleClassFormGroup.controls.duration.value ?? null,
      skillType: this.ensembleClassFormGroup.controls.skillType.value ?? undefined,
    };
  }

  @Debounce(200)
  getSuggestedTime(): void {
    if (
      this.filterParams.instrument.value.length && 
      this.filterParams.instructor.value.length &&
      this.getInstructorAvailability.scheduleStartDate
    ) {
      this.ensembleClassService
        .add(this.getInstructorAvailability, API_URL.ensembleClassesScheduleSummaries.getInstructorAvaibilableTimeSlots)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<SuggestedTimeSlot[]>) => {
            this.suggestedTimeSlots = response.result;
            this.selectedTimeSlot = null;
            this.ensembleClassFormGroup.controls.scheduleStartTime.reset();
            this.cdr.detectChanges();
          }
        });
    }
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getDayOfWeek(): void {
    const day = moment(this.ensembleClassFormGroup.controls.scheduleStartDate.value).day();
    this.setFormControlValue('daysOfSchedule', day);
  }

  checkCapacity(): void {
    if (this.ensembleClassFormGroup.invalid) {
      this.ensembleClassFormGroup.markAllAsTouched();
      return;
    }
    const selectedRoomId = this.ensembleClassFormGroup.get('roomId')?.value;
    const selectedRoom = this.rooms.find(room => room.roomDetail.id === selectedRoomId);
    const formCapacity = this.ensembleClassFormGroup.getRawValue().studentCapacity;

    if (formCapacity > +selectedRoom?.roomDetail.capacity!) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Alert',
          message: `Selected room capacity is ${selectedRoom?.roomDetail.capacity} which is is less than the required capacity.`,
          acceptBtnName: 'Book Anyway',
          rejectBtnName: 'Change'
        }
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.onSubmit();
        }
      });
      return;
    }
    this.onSubmit();
  }

  onSubmit(): void {
    this.ensembleClassFormGroup.markAsUntouched();
    this.showBtnLoader = true;
    this.cdr.detectChanges();
    this.ensembleClassService
      .add(
        {
          ...this.ensembleClassFormGroup.getRawValue(),
          scheduleStartDate: this.datePipe.transform(
            new Date(this.ensembleClassFormGroup.getRawValue().scheduleStartDate),
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleEndDate: moment(this.ensembleClassFormGroup.controls.scheduleStartDate.value).add(1, 'year').format(this.constants.dateFormats.yyyy_MM_DD),
          scheduleStartTime: this.datePipe.transform(
            this.ensembleClassFormGroup.getRawValue().scheduleStartTime,
            this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
          ),
          enrollLastDate: this.datePipe.transform(
            new Date(this.ensembleClassFormGroup.getRawValue().scheduleStartDate),
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleEndTime: this.datePipe.transform(
            this.ensembleClassFormGroup.getRawValue().scheduleEndTime,
            this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
          ),
          daysOfSchedule: [Number(this.ensembleClassFormGroup.getRawValue().daysOfSchedule)],
          ensembleClassName: this.ensembleClassFormGroup.getRawValue().ensembleClassName
        },
        API_URL.crud.create
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.closeSideNavFun();
          this.isEnsembleClassAdded.emit();
          this.toasterService.success(this.constants.successMessages.addedSuccessfully.replace('{item}', 'Ensemble Class'));
          this.showBtnLoader = false; 
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  closeSideNavFun(): void {
    this.closeSideNav.emit();
    this.filterParams.instrument.value = [];
    this.filterParams.instructor.value = [];
    this.ensembleClassFormGroup.reset();
  }

  asIsOrder(): number {
    return 1;
  }
}
