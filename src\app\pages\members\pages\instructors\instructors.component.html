<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isAddInstructorSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    [ngClass]="isAddInstructor ? 'sidebar-w-850' : 'lg-sidebar'"
    [disableClose]="true">
    <ng-container [ngTemplateOutlet]="isAddInstructor ? addInstructor : viewInstructor"></ng-container>
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="header-tab-only-btn">
      <button
        *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
        mat-raised-button
        color="primary"
        class="mat-primary-btn action-btn"
        type="button"
        (click)="openAddOrViewInstructor(true, null)">
        Add Instructor
      </button>
    </div>

    <div class="auth-page-wrapper auth-page-with-header">
      <div class="filter-and-count-wrapper mb-2">
        <div class="search-and-count-wrapper-auth">
          <div class="total-users">
            Total: <span>{{ totalCount }}</span>
          </div>
          <div class="search-bar">
            <mat-form-field class="search-bar-wrapper ms-3">
              <input
                matInput
                placeholder="Search.."
                [(ngModel)]="filters.searchTerm"
                (ngModelChange)="onSearchTermChanged()" />
              <mat-icon matTextPrefix>search</mat-icon>
            </mat-form-field>
          </div>
        </div>
        <div class="filter-wrapper">
          <div class="search-bar-wrapper">
            <app-multi-select
              [filterDetail]="filters.locationId"
              (selectedFilterValues)="getInstructors((currentPage = 1), pageSize)">
            </app-multi-select>
          </div>

          <div class="search-bar-wrapper">
            <app-multi-select
              [filterDetail]="filters.instrumentId"
              (selectedFilterValues)="getInstructors((currentPage = 1), pageSize)">
            </app-multi-select>
          </div>
        </div>
      </div>

      <div class="o-card">
        <div class="o-card-body">
          <div class="o-table">
            <div class="o-row o-header">
              <div class="o-cell first-cell">Instructor</div>
              <div class="o-cell">No. of clients</div>
              <div class="o-cell">Location</div>
              <div class="o-cell">Instrument</div>
            </div>
            <div class="dotted-divider"></div>
            <div class="content" [ngClass]="totalCount > 10 ? 'show-pagination' : 'hide-pagination'">
              <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : instructorTemplate"></ng-container>
            </div>
          </div>
        </div>
      </div>
      @if (totalCount > 10) {
        <pagination-controls
          id="instructor"
          [previousLabel]="''"
          [nextLabel]="''"
          (pageChange)="onPageChange($event)"
          [responsive]="true"
          class="pagination-controls"></pagination-controls>
      }
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #addInstructor>
  <app-add-instructor
    [selectedInstructorDetails]="selectedInstructorViewDetails"
    (closeSideNav)="closeAddOrEditInstructor($event)"
    (isInstructorAdded)="getInstructors(currentPage, pageSize)"></app-add-instructor>
</ng-template>

<ng-template #viewInstructor>
  <app-view-instructor
    (openEditSideNav)="openAddOrViewInstructor(true, selectedInstructorViewDetails)"
    (closeViewSideNav)="closeAddOrEditInstructor(null)"
    [currentUser$]="currentUser"
    [selectedInstructorViewDetails]="selectedInstructorViewDetails"></app-view-instructor>
</ng-template>

<ng-template #instructorTemplate>
  <ng-container [ngTemplateOutlet]="totalCount ? instructorTableContent : noDataFound"></ng-container>
</ng-template>

<ng-template #instructorTableContent>
  @for (
    instructor of instructors
      | paginate: { itemsPerPage: pageSize, currentPage: currentPage, totalItems: totalCount, id: "instructor" };
    track $index
  ) {
    <div class="o-row">
      <div
        class="o-cell first-cell instructor-name-photo-wrapper"
        (click)="openAddOrViewInstructor(false, instructor.instructorDetail)">
        @if (instructor.instructorDetail.profilePhoto) {
          <img [src]="instructor.instructorDetail.profilePhoto" class="me-2" alt="" />
        } @else {
          <div class="placeholder-name">
            <div>
              {{ getInitials(instructor.instructorDetail.name) | uppercase }}
            </div>
          </div>
        }
        <div class="text-truncate" [matTooltip]="instructor.instructorDetail.name">{{ instructor.instructorDetail.name | titlecase }}</div>
      </div>
      <div class="o-cell text-gray">{{ instructor.instructorDetail.students }}</div>
      <div class="o-cell text-gray">
        <div class="instrument-wrapper">
          @if (instructor.instructorDetail.instructorAvailability.length) {
            <img [src]="constants.staticImages.icons.location" class="me-1" alt="" />
            @for (instructorAvailability of instructor.instructorDetail.instructorAvailability; track $index) {
              <div class="instrument-item" *ngIf="$index < 1">{{ instructorAvailability.locationName }}</div>
              <div
                class="dot"
                *ngIf="
                  $index < 1 &&
                  getLocationDetails(instructor.instructorDetail.instructorAvailability).count - 1 !== $index
                "></div>
            }
            @if (getLocationDetails(instructor.instructorDetail.instructorAvailability).count > 1) {
              <div
                class="remaining-instrument-available-count"
                [matTooltip]="getLocationDetails(instructor.instructorDetail.instructorAvailability).names">
                {{ getLocationDetails(instructor.instructorDetail.instructorAvailability).count - 1 }}+
              </div>
            }
          } @else {
            -
          }
        </div>
      </div>
      <div class="o-cell text-gray">
        <div class="instrument-wrapper">
          @if (instructor.instructorDetail.instruments.length) {
            @for (instrument of instructor.instructorDetail.instruments; track $index) {
              <div class="instrument-item" *ngIf="$index < 1">{{ instrument.name }}</div>
              <div
                class="dot"
                *ngIf="$index < 1 && instructor.instructorDetail.instruments.length - 1 !== $index"></div>
            }
            @if (instructor.instructorDetail.instruments.length > 1) {
              <div
                class="remaining-instrument-available-count"
                [matTooltip]="getInstrumentNames(instructor.instructorDetail.instruments)">
                {{ instructor.instructorDetail.instruments.length - 1 }}+
              </div>
            }
          } @else {
            -
          }
        </div>
      </div>
    </div>

    @if ($index < instructors.length - 1) {
      <div class="dotted-divider"></div>
    }
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-wrapper">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
