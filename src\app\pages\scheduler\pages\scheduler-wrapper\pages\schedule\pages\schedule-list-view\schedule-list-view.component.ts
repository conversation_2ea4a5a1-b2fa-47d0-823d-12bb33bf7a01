import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { MbscCalendarEvent, MbscEventcalendarOptions, MbscModule, MbscPopup } from '@mobiscroll/angular';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonUtils } from 'src/app/shared/utils';
import { MatIconModule } from '@angular/material/icon';
import { SharedModule } from 'src/app/shared/shared.module';
import { SchedulerService } from '../../services';
import { ClassTypes, ScheduleDetailsView } from '../../models';
import { POPUP_OPTIONS } from 'src/app/shared/constants';
import { SchedulerDetailPopupComponent } from '../scheduler-detail-popup/scheduler-detail-popup.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AssignedInstructors } from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { Account } from 'src/app/auth/models/user.model';

const DEPENDENCIES = {
  MODULES: [MbscModule, CommonModule, MatIconModule, SharedModule, MatTooltipModule],
  COMPONENTS: [SchedulerDetailPopupComponent]
};

@Component({
  selector: 'app-schedule-list-view',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './schedule-list-view.component.html',
  styleUrl: './schedule-list-view.component.scss'
})
export class ScheduleListViewComponent extends BaseComponent {
  @Input() showScheduleForDate!: Date;
  @Input() schedulerData!: MbscCalendarEvent[];
  @Input() isLoading!: boolean;
  @Input() currentUser$!: Account | null;

  @ViewChild('eventDetailsPopup', { static: false }) eventDetailsPopup!: MbscPopup;
  @ViewChild(SchedulerDetailPopupComponent) schedulerDetailPopupComponent!: SchedulerDetailPopupComponent;

  @Output() refreshScheduleData = new EventEmitter<void>();

  detailsAnchor!: EventTarget | null;
  selectedEvent!: ScheduleDetailsView | undefined;
  popupOptions = POPUP_OPTIONS;
  classTypes = ClassTypes;

  @Output() openScheduleUpdateModal = new EventEmitter<ScheduleDetailsView>();

  calendarOptions: MbscEventcalendarOptions = {
    view: {
      agenda: { type: 'day' }
    }
  };

  constructor(public readonly schedulerService: SchedulerService) {
    super();
  }

  getTimeDiff(start: string, end: string): number {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  getStudentDisplayText(data: any): string {
    const studentName = data.original.studentDetails[0].studentName;
    const additionalCount = data.original.studentDetails.length - 1;

    return additionalCount ? `${studentName} +${additionalCount}` : studentName;
  }

  openEventDetailsPopup(args: ScheduleDetailsView | undefined, event: MouseEvent): void {
    // Check if this is a leave event and handle differently
    if (args && (args as any).isLeave) {
      // For leave events, you might want to show a different popup or handle differently
      console.log('Leave event clicked:', args);
      // For now, we'll skip opening the popup for leave events
      // You can implement a separate leave details popup here if needed
      return;
    }

    if (args?.id) {
      this.selectedEvent = args;
    }
    this.detailsAnchor = event.currentTarget || event.target;
    this.eventDetailsPopup?.open();
  }

  getInitials(name: string | undefined): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }

  getInstructorNames(array: AssignedInstructors[]) {
    return array
      .slice(1)
      .map(item => item.instructorName)
      .join(', ');
  }

  onEditLesson(scheduleDetail: ScheduleDetailsView): void {
    this.closeEventDetailsPopup(false);
    this.openScheduleUpdateModal.emit(scheduleDetail);
  }

  closeEventDetailsPopup(shouldRefreshScheduleData: boolean): void {
    if (shouldRefreshScheduleData) {
      this.refreshScheduleData.emit();
    }
    this.selectedEvent = undefined;
    this.eventDetailsPopup.close();
  }
}
