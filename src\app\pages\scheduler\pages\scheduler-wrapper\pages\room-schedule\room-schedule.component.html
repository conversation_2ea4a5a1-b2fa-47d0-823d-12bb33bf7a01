<div class="o-card schedule-wrapper">
  <div class="o-card-body">
    <div class="calender-action-filter-wrapper">
      <div class="calender-actions">
        <div class="action-border today-btn" (click)="setDateToCurrentDate()">Today</div>
        <div class="change-date-pre-next-wrapper">
          <div class="action-border prev-next-icon-wrapper" (click)="goToPreviousDate()">
            <mat-icon>keyboard_arrow_left</mat-icon>
          </div>
          <mat-form-field class="datepicker">
            <input
              matInput
              [matDatepicker]="picker"
              [ngModel]="showScheduleForDate"
              (dateChange)="onCalendarDateChange($event.value)" />
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
          <div class="action-border calender-icon" (click)="picker.open()">
            <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
          </div>
          <div class="action-border prev-next-icon-wrapper" (click)="goToNextDate()">
            <mat-icon>keyboard_arrow_right</mat-icon>
          </div>
        </div>
        <div class="current-date">
          {{ firstDateOfCurrentWeek | date: constants.fullDate }} -
          {{ lastDateOfCurrentWeek | date: constants.fullDate }}
        </div>
      </div>
    </div>
    <div class="filters-wrapper">
      <div class="location-filter-wrapper">
        <mat-form-field class="search-bar-wrapper">
          <mat-select [(ngModel)]="selectedLocation" (selectionChange)="getRooms(); setDateToCurrentDate(false)">
            <mat-option [value]="0" [hidden]="true">Select Location</mat-option>
            <mat-option *ngFor="let schoolLocation of schoolLocations" [value]="schoolLocation.schoolLocations.id">
              {{ schoolLocation.schoolLocations.locationName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <app-multi-select
        [filterDetail]="filterParams.instructor"
        (selectedFilterValues)="setDateToCurrentDate(false)"></app-multi-select>
      <app-multi-select
        [filterDetail]="filterParams.instrument"
        (selectedFilterValues)="setDateToCurrentDate(false)"></app-multi-select>
    </div>
    <div class="room-scheduler-wrapper">
      <ng-container [ngTemplateOutlet]="isLoading ? showLoader : showRoomScheduler"></ng-container>
    </div>
  </div>
</div>

<ng-template #showRoomScheduler>
  @if (resources && resources.length) {
    <mbsc-eventcalendar
      [data]="schedulerData"
      [resources]="resources"
      [options]="calendarSettings"
      [resourceTemplate]="roomResourceInfoTemplate"
      [scheduleEventContentTemplate]="roomScheduleItemTemplate"
      [dayTemplate]="schedulerDayHeaderTemplate"
      class="room-scheduler"
      themeVariant="light"
      theme="ios"
      [selectedDate]="showScheduleForDate">
      <ng-template #schedulerDayHeaderTemplate let-day>
        <div class="room-scheduler-date-header-wrapper">
          <div class="room-scheduler-day">
            {{ day.date | date: constants.dateFormats.EEE_d }}
          </div>
        </div>
      </ng-template>

      <ng-template #roomResourceInfoTemplate let-resource>
        <div class="room-info-wrapper" (click)="openRoomInfoPopup(resource, $event)">
          <div class="room-name">{{ resource.roomName }}</div>
        </div>
      </ng-template>

      <ng-template #roomScheduleItemTemplate let-args>
        <div
          class="room-scheduler-item-wrapper"
          [ngStyle]="{
            background: getFirstInstrumentOfInstructor(args.original.scheduledInstruments).colorCode
          }"
          (click)="openEventDetailsPopup(args.original, $event)">
          <div
            class="schedule-border"
            [ngStyle]="{
              'background-color': getFirstInstrumentOfInstructor(args.original.scheduledInstruments)
                .fontColorCode
            }"></div>
          <div class="scheduler-info-wrapper">
            <div
              class="instructor-name-action-wrapper"
              [ngStyle]="{
                color: getFirstInstrumentOfInstructor(args.original.scheduledInstruments).fontColorCode
              }">
              <div class="instructor-name">{{ args.original.instructorDetails.name }}</div>
            </div>
            <div class="schedule-time-info">
              {{ args.original.start | date: "shortTime" }}-{{ args.original.end | date: "shortTime" }}
            </div>
          </div>
        </div>
      </ng-template>
    </mbsc-eventcalendar>

    <mbsc-popup #roomDetailsPopup [anchor]="detailsAnchor" [options]="popupOptions">
      <div class="room-scheduler-details-wrapper">
        <div class="room-scheduler-header">
          <div class="name" [ngStyle]="{ color: selectedEvent && selectedEvent.scheduledInstruments[0].fontColorCode }">
            {{ selectedEvent?.instructorDetails?.name }}
          </div>
          <div class="d-flex align-items-center flex-wrap">
            <ng-container
              [ngTemplateOutlet]="eventInfoTemplate"
              [ngTemplateOutletContext]="{
                img: constants.staticImages.icons.location,
                text: selectedEvent?.locationName
              }"></ng-container>
            <div class="dot"></div>
            <ng-container
              [ngTemplateOutlet]="eventInfoTemplate"
              [ngTemplateOutletContext]="{
                img: constants.staticImages.icons.home,
                text: selectedEvent?.roomName
              }"></ng-container>
          </div>
        </div>
        <div class="divider"></div>
        <div class="name mt-2">Instructor Details</div>
        <div class="event-info-wrapper">
          <div class="event-info-content">
            <img [src]="constants.staticImages.icons.musicWave" alt="" />
            <div class="info-text">
              <div class="instruments-wrapper">
                @for (roomInstrument of selectedEvent?.instructorDetails?.instruments; track $index) {
                  <div class="instrument-info">
                    {{ roomInstrument.instrumentName }}
                    <span class="primary-color">{{ roomInstrument.instrumentGrade }}</span>
                  </div>
                  @if ($index < selectedEvent?.instructorDetails?.instruments?.length! - 1) {
                    <div class="dot"></div>
                  }
                }
              </div>
            </div>
          </div>
          <ng-container
            [ngTemplateOutlet]="eventInfoTemplate"
            [ngTemplateOutletContext]="{
              img: constants.staticImages.icons.phone,
              text: selectedEvent?.instructorDetails?.phoneNumber
            }"></ng-container>
          <ng-container
            [ngTemplateOutlet]="eventInfoTemplate"
            [ngTemplateOutletContext]="{
              img: constants.staticImages.icons.email,
              text: selectedEvent?.instructorDetails?.email
            }"></ng-container>
          <ng-container
            [ngTemplateOutlet]="eventInfoTemplate"
            [ngTemplateOutletContext]="{
              img: constants.staticImages.icons.timeCircleClock,
              text:
                (selectedEvent?.start | date: 'shortTime') +
                ' - ' +
                (selectedEvent?.end | date: 'shortTime') +
                ', ' +
                (selectedEvent?.scheduleDate | date: constants.dateFormats.EEE_MMM_dd)
            }"></ng-container>
        </div>
        @if (currentDateTime! <= selectedEvent?.start!) {
          <div class="action-btn-wrapper">
            <button
              mat-raised-button
              color="primary"
              class="mat-red-btn action-btn me-2"
              type="button"
              (click)="onDeleteRoomSchedule(selectedEvent?.id)">
              Remove
            </button>
            <button
              mat-raised-button
              color="primary"
              class="mat-primary-btn action-btn"
              type="button"
              (click)="navigateToEditSchedule(selectedEvent)">
              Edit Schedule
            </button>
          </div>
        }
      </div>
    </mbsc-popup>

    <mbsc-popup #roomInfoPopup [anchor]="infoAnchor" [options]="popupOptions">
      <div class="room-scheduler-details-wrapper">
        <div class="room-scheduler-header p-0">
          <div class="name pb-2">
            {{ selectedRoomInfo?.roomName }}
          </div>
        </div>
        <div class="event-info-wrapper">
          <ng-container
            [ngTemplateOutlet]="eventInfoTemplate"
            [ngTemplateOutletContext]="{
              img: constants.staticImages.icons.location,
              text: getLocationNameFromValue(selectedRoomInfo?.locationId),
              roomInfo: true
            }"></ng-container>
          <ng-container
            [ngTemplateOutlet]="eventInfoTemplate"
            [ngTemplateOutletContext]="{
              img: constants.staticImages.icons.timeCircleClock,
              text: '8:00 AM - 10:00 PM',
              roomInfo: true
            }"></ng-container>
          @if (selectedRoomInfo?.roomInstrumentList?.length) {
            <div class="room-info-content">
              <img [src]="constants.staticImages.icons.instrumentIcon" alt="" />
              <div class="room-info-text">
                <div class="instruments-wrapper">
                  @for (roomInstrument of selectedRoomInfo?.roomInstrumentList; track $index) {
                    <div class="instrument-info">{{ roomInstrument.quantity }} {{ roomInstrument.instrumentName }}</div>
                    @if (selectedRoomInfo!.roomInstrumentList.length - 1 !== $index) {
                      <div class="dot"></div>
                    }
                  }
                </div>
              </div>
            </div>
          }
          <ng-container
            [ngTemplateOutlet]="eventInfoTemplate"
            [ngTemplateOutletContext]="{
              img: constants.staticImages.icons.people,
              text: (selectedRoomInfo?.capacity ?? 0) + ' Client Capacity',
              roomInfo: true
            }"></ng-container>
        </div>
      </div>
    </mbsc-popup>
  } @else {
    <div class="no-data-found-wrapper">
      <h3>NO DATA FOUND!</h3>
    </div>
  }
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>

<ng-template #eventInfoTemplate let-img="img" let-text="text" let-roomInfo="roomInfo">
  <div class="event-info-content">
    <img [src]="img" [ngClass]="{ 'gray-text': roomInfo }" alt="" />
    <div class="info-text" [ngClass]="{ 'gray-text': roomInfo }">{{ text }}</div>
  </div>
</ng-template>
