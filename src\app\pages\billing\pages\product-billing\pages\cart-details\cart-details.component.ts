import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { PurchasedItems } from 'src/app/pages/shop/models';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule]
};

@Component({
  selector: 'app-cart-details',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './cart-details.component.html',
  styleUrl: './cart-details.component.scss'
})
export class CartDetailsComponent extends BaseComponent implements OnChanges {
  @Input() selectedCartItems!: PurchasedItems | null;
  @Output() closeSideNav = new EventEmitter<void>();
  
  constructor() {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedCartItems']?.currentValue) {
      this.selectedCartItems = changes['selectedCartItems']?.currentValue;
    }
  }

  closeSideNavFun(): void {
    this.closeSideNav.emit();
  }
}
